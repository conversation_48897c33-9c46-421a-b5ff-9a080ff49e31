# Copyright 2013 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

name: coverage

on:
  push:
    branches:
      - master
    paths:
      - 'packages/flutter/**'

permissions: read-all

jobs:
  build:
    name: coverage
    runs-on: ubuntu-latest
    if: ${{ github.repository == 'flutter/flutter' }}
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
      - name: ./bin/flutter test --coverage
        run: pushd packages/flutter;../../bin/flutter test --coverage -j 1;popd
      - name: upload coverage
        uses: codecov/codecov-action@0565863a31f2c772f9f0395002a31e3f06189574
        with:
          files: packages/flutter/coverage/lcov.info
          verbose: true
