import 'package:flutter/material.dart';
import 'package:offline_menu_translator/ui/model_selection_screen.dart';
import 'package:offline_menu_translator/ui/theme.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Offline Menu Translator',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      home: const ModelSelectionScreen(),
    );
  }
}
