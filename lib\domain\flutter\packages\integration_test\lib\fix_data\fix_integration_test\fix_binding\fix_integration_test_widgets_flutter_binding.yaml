# Copyright 2014 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# For details regarding the *Flutter Fix* feature, see
# https://flutter.dev/to/flutter-fix

# Please add new fixes to the top of the file, separated by one blank line
# from other fixes. In a comment, include a link to the PR where the change
# requiring the fix was made.

# Every fix must be tested. See the
# flutter/packages/integration_test/test_fixes/README.md file for instructions
# on testing these data driven fixes.

# For documentation about this file format, see
# https://dart.dev/go/data-driven-fixes.

# * Fixes in this file are for IntegrationTestWidgetsFlutterBinding from the
#   integration_test/integration_test.dart file. *

version: 1
transforms:
  # Changes made in https://github.com/flutter/flutter/pull/89952
  - title: "Remove timeout"
    date: 2023-06-26
    element:
      uris: [ 'integration_test.dart' ]
      method: 'runTest'
      inClass: 'IntegrationTestWidgetsFlutterBinding'
    changes:
      - kind: 'removeParameter'
        name: 'timeout'
