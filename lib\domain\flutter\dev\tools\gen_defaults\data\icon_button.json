{"version": "6_1_0", "md.comp.icon-button.disabled.icon.color": "onSurface", "md.comp.icon-button.disabled.icon.opacity": 0.38, "md.comp.icon-button.focus.indicator.color": "secondary", "md.comp.icon-button.focus.indicator.outline.offset": "md.sys.state.focus-indicator.outer-offset", "md.comp.icon-button.focus.indicator.thickness": "md.sys.state.focus-indicator.thickness", "md.comp.icon-button.icon.size": 24.0, "md.comp.icon-button.selected.focus.icon.color": "primary", "md.comp.icon-button.selected.focus.state-layer.color": "primary", "md.comp.icon-button.selected.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.icon-button.selected.hover.icon.color": "primary", "md.comp.icon-button.selected.hover.state-layer.color": "primary", "md.comp.icon-button.selected.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.icon-button.selected.icon.color": "primary", "md.comp.icon-button.selected.pressed.icon.color": "primary", "md.comp.icon-button.selected.pressed.state-layer.color": "primary", "md.comp.icon-button.selected.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.icon-button.state-layer.height": 40.0, "md.comp.icon-button.state-layer.shape": "md.sys.shape.corner.full", "md.comp.icon-button.state-layer.width": 40.0, "md.comp.icon-button.unselected.focus.icon.color": "onSurfaceVariant", "md.comp.icon-button.unselected.focus.state-layer.color": "onSurfaceVariant", "md.comp.icon-button.unselected.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.icon-button.unselected.hover.icon.color": "onSurfaceVariant", "md.comp.icon-button.unselected.hover.state-layer.color": "onSurfaceVariant", "md.comp.icon-button.unselected.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.icon-button.unselected.icon.color": "onSurfaceVariant", "md.comp.icon-button.unselected.pressed.icon.color": "onSurfaceVariant", "md.comp.icon-button.unselected.pressed.state-layer.color": "onSurfaceVariant", "md.comp.icon-button.unselected.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity"}