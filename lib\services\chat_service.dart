import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';

class ChatService {
  static ChatService? _instance;
  static ChatService get instance {
    _instance ??= ChatService._();
    return _instance!;
  }

  ChatService._();

  String? _activeModelId;
  Map<String, dynamic>? _activeModelInfo;

  void setActiveModel(String modelId, Map<String, dynamic> modelInfo) {
    _activeModelId = modelId;
    _activeModelInfo = modelInfo;
  }

  String? get activeModelId => _activeModelId;
  Map<String, dynamic>? get activeModelInfo => _activeModelInfo;

  /// Generate a chat response stream
  Stream<String> generateResponse(String prompt, {Uint8List? imageBytes}) async* {
    if (_activeModelId == null) {
      yield 'Error: No model loaded. Please select and download a model first.';
      return;
    }

    // Simulate AI thinking time
    await Future.delayed(const Duration(milliseconds: 500));

    // Generate response based on model type and prompt
    final response = _generateResponseText(prompt, imageBytes);
    
    // Stream the response word by word for realistic effect
    final words = response.split(' ');
    for (int i = 0; i < words.length; i++) {
      if (i == 0) {
        yield words[i];
      } else {
        yield ' ${words[i]}';
      }
      
      // Variable delay for realistic typing effect
      final delay = _getTypingDelay(words[i]);
      await Future.delayed(Duration(milliseconds: delay));
    }
  }

  String _generateResponseText(String prompt, Uint8List? imageBytes) {
    final isImagePrompt = imageBytes != null;
    final modelName = _activeModelInfo?['name'] ?? 'AI Model';
    
    // Different responses based on model and input type
    if (isImagePrompt) {
      return _generateImageResponse(prompt);
    } else {
      return _generateTextResponse(prompt);
    }
  }

  String _generateImageResponse(String prompt) {
    final responses = [
      '''# 🍽️ Menu Translation

I can see this appears to be a restaurant menu. Here's my translation:

## Appetizers
• **Spring Rolls** - Fresh vegetables wrapped in rice paper, served with sweet chili sauce
• **Soup of the Day** - Chef's special daily soup with seasonal ingredients
• **Bruschetta** - Toasted bread with tomatoes, basil, and garlic

## Main Courses
• **Grilled Salmon** - Fresh Atlantic salmon with lemon herb butter
• **Chicken Teriyaki** - Tender chicken breast with teriyaki glaze and steamed rice
• **Vegetarian Pasta** - Penne with seasonal vegetables in marinara sauce
• **Beef Stir Fry** - Tender beef strips with mixed vegetables

## Desserts
• **Chocolate Cake** - Rich chocolate layer cake with vanilla ice cream
• **Fresh Fruit Platter** - Seasonal fruits with honey yogurt dip
• **Tiramisu** - Classic Italian dessert with coffee and mascarpone

## Beverages
• **Fresh Juices** - Orange, apple, or mixed fruit
• **Coffee & Tea** - Espresso, cappuccino, green tea, black tea
• **Soft Drinks** - Coca-Cola, Sprite, local sodas

*Note: Prices and availability may vary. Please confirm with your server.*''',

      '''# 📋 Menu Analysis & Translation

Based on the image you've shared, I can identify several menu items:

## 🥗 Starters
- **Mixed Salad** - Fresh greens with house dressing
- **Garlic Bread** - Toasted bread with garlic butter
- **Cheese Platter** - Selection of local cheeses

## 🍖 Main Dishes
- **Grilled Chicken** - Herb-marinated chicken breast
- **Fish of the Day** - Fresh catch prepared to your liking
- **Pasta Carbonara** - Traditional Italian pasta with eggs and bacon
- **Vegetable Curry** - Spiced vegetables in coconut sauce

## 🍰 Desserts
- **Ice Cream** - Vanilla, chocolate, or strawberry
- **Apple Pie** - Homemade with cinnamon
- **Cheese Cake** - New York style

## ☕ Drinks
- **Coffee** - Espresso, americano, latte
- **Tea** - Black, green, herbal varieties
- **Fresh Juices** - Orange, apple, pineapple

*Translation confidence: High. Menu appears to be in good condition for analysis.*''',

      '''# 🌍 International Menu Translation

I've analyzed your menu image and here's the translation:

## 🍜 Soups & Appetizers
• **Tom Yum Soup** - Spicy and sour Thai soup with shrimp
• **Dumplings** - Steamed or fried, with pork or vegetable filling
• **Satay Skewers** - Grilled meat skewers with peanut sauce

## 🍛 Rice & Noodle Dishes
• **Pad Thai** - Stir-fried rice noodles with tamarind sauce
• **Fried Rice** - Wok-fried rice with vegetables and choice of protein
• **Ramen** - Japanese noodle soup with rich broth

## 🥘 Curry Dishes
• **Green Curry** - Thai green curry with coconut milk
• **Red Curry** - Spicy red curry with bamboo shoots
• **Massaman Curry** - Mild curry with potatoes and peanuts

## 🍹 Beverages
• **Thai Iced Tea** - Sweet tea with condensed milk
• **Fresh Coconut Water** - Served in coconut shell
• **Mango Smoothie** - Fresh mango blended with ice

*Dietary notes: Vegetarian and vegan options available upon request.*'''
    ];

    return responses[Random().nextInt(responses.length)];
  }

  String _generateTextResponse(String prompt) {
    final lowerPrompt = prompt.toLowerCase();
    
    if (lowerPrompt.contains('hello') || lowerPrompt.contains('hi')) {
      return '''Hello! I'm your AI menu translator powered by ${_activeModelInfo?['name'] ?? 'Gemma 3N'}. 

I can help you:
• 📸 Translate menu images to any language
• 🍽️ Explain dishes and ingredients
• 🌶️ Identify spice levels and dietary information
• 💡 Suggest similar dishes you might like

Just upload a photo of any menu and I'll translate it for you instantly!''';
    }
    
    if (lowerPrompt.contains('help')) {
      return '''# 🤖 How I Can Help You

## 📱 **Menu Translation**
Upload a photo of any restaurant menu and I'll:
- Translate all items to your preferred language
- Explain unfamiliar dishes
- Identify ingredients and cooking methods

## 🌍 **Supported Languages**
I can translate menus to/from:
- English, Spanish, French, German
- Japanese, Korean, Chinese
- Thai, Vietnamese, Arabic
- And many more!

## 🍽️ **Food Information**
I can also provide:
- Dietary restrictions (vegetarian, vegan, gluten-free)
- Spice levels and heat ratings
- Cultural context about dishes
- Ingredient explanations

## 📸 **How to Use**
1. Take a clear photo of the menu
2. Upload it using the camera button
3. I'll analyze and translate everything
4. Ask follow-up questions if needed!

Ready to translate your first menu?''';
    }
    
    if (lowerPrompt.contains('language')) {
      return '''# 🌐 Language Support

I support translation between many languages including:

## 🇪🇺 **European Languages**
- English, Spanish, French, German
- Italian, Portuguese, Dutch
- Polish, Russian, Swedish

## 🇦🇸 **Asian Languages**  
- Japanese, Korean, Chinese (Simplified & Traditional)
- Thai, Vietnamese, Indonesian
- Hindi, Arabic, Hebrew

## 🍽️ **Specialized Food Terminology**
I'm trained on culinary terms and can accurately translate:
- Regional dish names
- Cooking techniques
- Ingredient lists
- Dietary information

Which language would you like me to translate to? You can change this in the language selector at the top of the screen.''';
    }

    // Default response
    return '''I'm ready to help translate any menu for you! 

To get started:
1. 📸 **Take a photo** of the menu using the camera button
2. 🔄 **I'll translate** all the items automatically  
3. 💬 **Ask questions** about any dishes you're curious about

I can translate between many languages and explain:
- What each dish contains
- How spicy or mild items are
- Vegetarian/vegan options
- Cultural background of dishes

Upload a menu photo and let's begin!''';
  }

  int _getTypingDelay(String word) {
    // Simulate realistic typing speed
    final baseDelay = 50;
    final wordLength = word.length;
    
    // Longer words take slightly more time
    final lengthDelay = wordLength * 5;
    
    // Add some randomness for natural feel
    final randomDelay = Random().nextInt(30);
    
    return baseDelay + lengthDelay + randomDelay;
  }

  /// Check if model is ready
  bool get isModelReady => _activeModelId != null;

  /// Get model capabilities
  List<String> getModelCapabilities() {
    if (_activeModelInfo == null) return [];
    return List<String>.from(_activeModelInfo!['capabilities'] ?? []);
  }
}
