import 'dart:async';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:flutter_gemma/flutter_gemma.dart';
import 'package:flutter_gemma/core/chat.dart';
import 'package:flutter_gemma/core/message.dart';
import 'package:flutter_gemma/core/model.dart';
import 'package:flutter_gemma/pigeon.g.dart';
import 'package:offline_menu_translator/domain/download_model.dart';
import 'package:path_provider/path_provider.dart';

/// Real implementation using flutter_gemma library
class RealGemmaDataSource {
  final DownloadModel model;
  final FlutterGemmaPlugin _gemma = FlutterGemmaPlugin.instance;

  InferenceModel? _inferenceModel;
  InferenceChat? _chat;

  RealGemmaDataSource({required this.model});

  /// Get the model file path
  Future<String> getModelPath() async {
    final directory = await getApplicationDocumentsDirectory();
    return '${directory.path}/${model.modelFilename}';
  }

  /// Check if model exists locally
  Future<bool> checkModelExistence() async {
    try {
      return await _gemma.modelManager.isModelInstalled;
    } catch (e) {
      debugPrint('Error checking model existence: $e');
      return false;
    }
  }

  /// Download model from network with progress tracking
  Future<void> downloadModel({
    required String token,
    required Function(double) onProgress,
  }) async {
    try {
      debugPrint('🔥 Starting real flutter_gemma download: ${model.modelUrl}');

      // Use flutter_gemma's download functionality
      final downloadStream = _gemma.modelManager
          .downloadModelFromNetworkWithProgress(model.modelUrl);

      await for (final progress in downloadStream) {
        debugPrint(
          '⬇️ Download progress: ${(progress * 100).toStringAsFixed(1)}%',
        );
        onProgress(progress.toDouble());
      }

      debugPrint('✅ Model download completed successfully!');
    } catch (e) {
      debugPrint('💥 Error downloading model: $e');
      rethrow;
    }
  }

  /// Initialize the model for inference
  Future<bool> initializeModel() async {
    try {
      debugPrint('🚀 Initializing flutter_gemma model...');

      // Check if model is installed
      if (!await _gemma.modelManager.isModelInstalled) {
        throw Exception('Model not found. Please download first.');
      }

      // Set model path
      final modelPath = await getModelPath();
      await _gemma.modelManager.setModelPath(modelPath);

      // Create inference model
      _inferenceModel = await _gemma.createModel(
        modelType: ModelType.gemmaIt,
        preferredBackend: PreferredBackend.gpu,
        maxTokens: 4096,
        supportImage: true,
        maxNumImages: 1,
      );

      // Create chat instance
      _chat = await _inferenceModel!.createChat(
        temperature: 0.8,
        randomSeed: 1,
        topK: 1,
        supportImage: true,
      );

      debugPrint('✅ Model initialized successfully!');
      return true;
    } catch (e) {
      debugPrint('💥 Error initializing model: $e');
      return false;
    }
  }

  /// Generate response stream for given prompt and optional image
  Stream<String> generateResponse(
    String prompt, {
    Uint8List? imageBytes,
  }) async* {
    if (_chat == null) {
      throw Exception('Model not initialized. Call initializeModel() first.');
    }

    try {
      debugPrint(
        '🤖 Generating response for: ${prompt.substring(0, prompt.length.clamp(0, 50))}...',
      );

      // Create message
      Message message;
      if (imageBytes != null) {
        message = Message.withImage(
          text: prompt,
          imageBytes: imageBytes,
          isUser: true,
        );
        debugPrint('📸 Processing image with text prompt');
      } else {
        message = Message.text(text: prompt, isUser: true);
        debugPrint('💬 Processing text-only prompt');
      }

      // Add message to chat
      await _chat!.addQueryChunk(message);

      // Get response stream
      final responseStream = _chat!.generateChatResponseAsync();

      await for (final token in responseStream) {
        yield token;
      }

      debugPrint('✅ Response generation completed');
    } catch (e) {
      debugPrint('💥 Error generating response: $e');
      yield 'Error: Failed to generate response. Please try again.';
    }
  }

  /// Clean up resources
  Future<void> dispose() async {
    try {
      await _inferenceModel?.close();
      _inferenceModel = null;
      _chat = null;
      debugPrint('🧹 Resources cleaned up');
    } catch (e) {
      debugPrint('⚠️ Error during cleanup: $e');
    }
  }
}
