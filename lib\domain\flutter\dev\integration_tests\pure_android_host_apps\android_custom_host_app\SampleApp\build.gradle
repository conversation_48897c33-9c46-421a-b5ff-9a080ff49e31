// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

apply plugin: 'com.android.application'

android {
    namespace = "io.flutter.add2app"
    compileSdk = 35

    // Flutter's CI installs the NDK at a non-standard path.
    // This non-standard structure is initially created by
    // https://github.com/flutter/engine/blob/3.27.0/tools/android_sdk/create_cipd_packages.sh.
    String systemNdkPath = System.getenv("ANDROID_NDK_PATH")
    if (systemNdkPath != null) {
        ndkVersion = "26.3.11579264" // This version must exactly match the version of the NDK that the recipe pulls from CIPD.
        ndkPath = systemNdkPath
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    defaultConfig {
        applicationId "io.flutter.add2app"
        minSdk = 21
        targetSdk = 35
        versionCode 1
        versionName "1.0"
    }
}

dependencies {
    implementation project(':flutter')
    implementation 'androidx.appcompat:appcompat:1.7.0'
}
