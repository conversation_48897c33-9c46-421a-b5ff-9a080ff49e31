// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include <map>
#include <set>
#include "flutter/shell/platform/darwin/ios/framework/Source/KeyCodeMap_Internal.h"

// DO NOT EDIT -- DO NOT EDIT -- DO NOT EDIT
// This file is generated by
// flutter/flutter:dev/tools/gen_keycodes/bin/gen_keycodes.dart and should not
// be edited directly.
//
// Edit the template
// flutter/flutter:dev/tools/gen_keycodes/data/ios_key_code_map_mm.tmpl instead.
//
// See flutter/flutter:dev/tools/gen_keycodes/README.md for more information.

@@@MASK_CONSTANTS@@@

// Maps iOS-specific key code values representing PhysicalKeyboardKey.
//
// iOS doesn't provide a scan code, but a virtual keycode to represent a physical key.
const std::map<uint32_t, uint64_t> keyCodeToPhysicalKey = {
@@@IOS_SCAN_CODE_MAP@@@
};

// Maps iOS-specific virtual key code values to logical keys representing
// LogicalKeyboardKey
const std::map<uint32_t, uint64_t> keyCodeToLogicalKey = {
@@@IOS_KEYCODE_LOGICAL_MAP@@@
};

// Maps iOS-specific virtual key codes to an equivalent modifier flag enum
// value.
const std::map<uint32_t, ModifierFlag> keyCodeToModifierFlag = {
@@@KEYCODE_TO_MODIFIER_FLAG_MAP@@@
};

// Maps modifier flag enum values to an iOS-specific virtual key code.
const std::map<ModifierFlag, uint32_t> modifierFlagToKeyCode = {
@@@MODIFIER_FLAG_TO_KEYCODE_MAP@@@
};

// A set of virtual key codes mapping to function keys, so that may be
// identified as such.
const std::set<uint32_t> functionKeyCodes = {
@@@IOS_FUNCTION_KEY_SET@@@
};

API_AVAILABLE(ios(13.4))
NSDictionary<NSString*, NSNumber*>* specialKeyMapping = @{
@@@SPECIAL_KEY_MAPPING@@@
};

@@@SPECIAL_KEY_CONSTANTS@@@
