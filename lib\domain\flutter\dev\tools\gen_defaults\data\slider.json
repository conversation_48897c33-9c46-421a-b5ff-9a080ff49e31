{"version": "6_1_0", "md.comp.slider.active.handle.height": 44.0, "md.comp.slider.active.handle.leading-space": 6.0, "md.comp.slider.active.handle.padding": 6.0, "md.comp.slider.active.handle.shape": "md.sys.shape.corner.full", "md.comp.slider.active.handle.trailing-space": 6.0, "md.comp.slider.active.handle.width": 4.0, "md.comp.slider.active.stop-indicator.container.color": "onPrimary", "md.comp.slider.active.stop-indicator.container.opacity": 1.0, "md.comp.slider.active.track.color": "primary", "md.comp.slider.active.track.height": 16.0, "md.comp.slider.active.track.shape": "md.sys.shape.corner.full", "md.comp.slider.active.track.shape.leading": "md.sys.shape.corner.full", "md.comp.slider.disabled.active.stop-indicator.container.color": "onInverseSurface", "md.comp.slider.disabled.active.track.color": "onSurface", "md.comp.slider.disabled.active.track.opacity": 0.38, "md.comp.slider.disabled.handle.color": "onSurface", "md.comp.slider.disabled.handle.opacity": 0.38, "md.comp.slider.disabled.handle.width": 4.0, "md.comp.slider.disabled.inactive.stop-indicator.container.color": "onSurface", "md.comp.slider.disabled.inactive.track.color": "onSurface", "md.comp.slider.disabled.inactive.track.opacity": 0.12, "md.comp.slider.focus.active.track.color": "primary", "md.comp.slider.focus.handle.width": 2.0, "md.comp.slider.focus.inactive.track.color": "secondaryContainer", "md.comp.slider.handle.color": "primary", "md.comp.slider.handle.height": 44.0, "md.comp.slider.handle.shape": "md.sys.shape.corner.full", "md.comp.slider.handle.width": 4.0, "md.comp.slider.hover.handle.width": 4.0, "md.comp.slider.inactive.stop-indicator.container.color": "onSecondaryContainer", "md.comp.slider.inactive.stop-indicator.container.opacity": 1.0, "md.comp.slider.inactive.track.color": "secondaryContainer", "md.comp.slider.inactive.track.height": 16.0, "md.comp.slider.inactive.track.shape": "md.sys.shape.corner.full", "md.comp.slider.pressed.active.track.color": "primary", "md.comp.slider.pressed.handle.color": "primary", "md.comp.slider.pressed.handle.width": 2.0, "md.comp.slider.pressed.inactive.track.color": "secondaryContainer", "md.comp.slider.slider-active-handle-color": "primary", "md.comp.slider.stop-indicator.color": "onSecondaryContainer", "md.comp.slider.stop-indicator.color-selected": "onPrimary", "md.comp.slider.stop-indicator.shape": "md.sys.shape.corner.full", "md.comp.slider.stop-indicator.size": 4.0, "md.comp.slider.stop-indicator.trailing-space": 6.0, "md.comp.slider.value-indicator.active.bottom-space": 12.0, "md.comp.slider.value-indicator.container.color": "inverseSurface", "md.comp.slider.value-indicator.label.label-text.color": "onInverseSurface", "md.comp.slider.value-indicator.label.label-text.text-style": "labelLarge"}