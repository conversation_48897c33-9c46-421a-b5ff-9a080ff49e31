# Describes the targets run in continuous integration environment.
#
# Flutter infra uses this file to generate a checklist of tasks to be performed
# for every commit.
#
# The recipes mentioned below refer to those in this repo:
#   https://flutter.googlesource.com/recipes/+/refs/heads/main/recipes/
#
# The "flutter_drone" recipe just defers to dev/bots/test.dart in this repo,
# with the shard set according to the "shard" key in this file.
#
# More information at:
#  * https://github.com/flutter/cocoon/blob/main/CI_YAML.md
enabled_branches:
  - master
  - flutter-\d+\.\d+-candidate\.\d+

platform_properties:
  staging_build_linux:
    properties:
      dependencies: >-
        [
          {"dependency": "curl", "version": "version:7.64.0"}
        ]
      os: Ubuntu
      cores: "8"
      device_type: none
      ignore_flakiness: "true"
  linux:
    properties:
      dependencies: >-
        [
          {"dependency": "curl", "version": "version:7.64.0"}
        ]
      os: Ubuntu
      cores: "8"
      device_type: none
  # The current android emulator config names can be found here:
  # https://chromium.googlesource.com/chromium/src.git/+/HEAD/tools/android/avd/proto
  # You may use those names for the android_virtual_device version. You may find the
  # avd_cipd_version by clicking on the latest available instance and looking for the
  # build_id:<Identifier#> here:
  # https://chrome-infra-packages.appspot.com/p/chromium/tools/android/avd/linux-amd64.
  linux_android_emu:
    properties:
      contexts: >-
        [
          "android_virtual_device"
        ]
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "android_virtual_device", "version": "android_35_google_apis_x64.textpb"},
          {"dependency": "avd_cipd_version", "version": "build_id:8733065022087935185"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      os: Ubuntu
      cores: "8"
      device_type: none
      kvm: "1"
  # linux_android_emu_unstable is intended to be how flutter-android proves the stability
  # of new combinations of dependencies.
  linux_android_emu_unstable:
    properties:
      contexts: >-
        [
          "android_virtual_device"
        ]
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "android_virtual_device", "version": "android_36_google_apis_x64.textpb"},
          {"dependency": "avd_cipd_version", "version": "build_id:8719362231152674241"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      os: Ubuntu
      cores: "8"
      device_type: none
      kvm: "1"
  linux_build_test:
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "curl", "version": "version:7.64.0"}
        ]
      os: Ubuntu
      cores: "8"
      device_type: none
  linux_android:
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "curl", "version": "version:7.64.0"}
        ]
      os: Linux
      device_type: "msm8952"

  linux_pixel_7pro:
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "curl", "version": "version:7.64.0"}
        ]
      os: Linux
      device_type: "Pixel 7 Pro"

  linux_mokey:
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "curl", "version": "version:7.64.0"}
        ]
      os: Linux
      device_type: "mokey"

  linux_galaxy_s24:
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "curl", "version": "version:7.64.0"}
        ]
      os: Linux
      device_type: "SM-S921U1"

  mac:
    properties:
      contexts: >-
        [
          "osx_sdk"
        ]
      dependencies: >-
        [
          {"dependency": "apple_signing", "version": "version:to_2025"}
        ]
      os: Mac-14
      device_type: none
      $flutter/osx_sdk : >-
        {
          "sdk_version": "16c5032a"
        }
  mac_arm64:
    properties:
      contexts: >-
        [
          "osx_sdk"
        ]
      dependencies: >-
        [
          {"dependency": "apple_signing", "version": "version:to_2025"}
        ]
      os: Mac-14
      device_type: none
      cpu: arm64
      $flutter/osx_sdk : >-
        {
          "sdk_version": "16c5032a"
        }
  mac_benchmark:
    properties:
      contexts: >-
        [
          "osx_sdk"
        ]
      dependencies: >-
        [
          {"dependency": "apple_signing", "version": "version:to_2025"}
        ]
      device_type: none
      mac_model: "Macmini8,1"
      os: Mac-14
      tags: >
        ["devicelab", "hostonly", "mac"]
      $flutter/osx_sdk : >-
        {
          "sdk_version": "16c5032a"
        }
  mac_x64:
    properties:
      contexts: >-
        [
          "osx_sdk"
        ]
      dependencies: >-
        [
          {"dependency": "apple_signing", "version": "version:to_2025"}
        ]
      os: Mac-14
      device_type: none
      cpu: x86
      $flutter/osx_sdk : >-
        {
          "sdk_version": "16c5032a"
        }
  mac_build_test:
    properties:
      contexts: >-
        [
          "osx_sdk"
        ]
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"},
          {"dependency": "apple_signing", "version": "version:to_2025"}
        ]
      os: Mac-14
      device_type: none
      cpu: x86
      $flutter/osx_sdk : >-
        {
          "sdk_version": "16c5032a"
        }
  mac_android:
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      os: Mac-14
      cpu: x86
      device_type: "msm8952"
  mac_arm64_android:
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      os: Mac-14
      cpu: arm64
      device_type: "msm8952"

  mac_mokey:
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      os: Mac-14
      cpu: x86
      device_type: "mokey"
  mac_arm64_mokey:
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      os: Mac-14
      cpu: arm64
      device_type: "mokey"

  mac_pixel_7pro:
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      os: Mac-14
      cpu: x86
      device_type: "Pixel 7 Pro"
  mac_ios:
    properties:
      contexts: >-
        [
          "osx_sdk_devicelab"
        ]
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"},
          {"dependency": "apple_signing", "version": "version:to_2025"}
        ]
      os: Mac-14
      cpu: x86
      device_os: iOS-17|iOS-18
      $flutter/osx_sdk : >-
        {
          "sdk_version": "16c5032a"
        }
  mac_x64_ios:
    properties:
      contexts: >-
        [
          "osx_sdk_devicelab"
        ]
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"},
          {"dependency": "apple_signing", "version": "version:to_2025"}
        ]
      os: Mac-14
      cpu: x86
      device_os: iOS-17|iOS-18
      $flutter/osx_sdk : >-
        {
          "sdk_version": "16c5032a"
        }
  mac_arm64_ios:
    properties:
      contexts: >-
        [
          "osx_sdk_devicelab"
        ]
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"},
          {"dependency": "apple_signing", "version": "none"}
        ]
      os: Mac-14
      cpu: arm64
      device_os: iOS-17|iOS-18
      $flutter/osx_sdk : >-
        {
          "sdk_version": "16c5032a"
        }
  windows:
    properties:
      os: Windows-10
      device_type: none
  windows_arm64:
    properties:
      # The arch can be removed after https://github.com/flutter/flutter/issues/135722.
      arch: arm
      os: Windows
      cpu: arm64
  windows_android:
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      os: Windows-10
      device_type: "msm8952"
  windows_mokey:
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      os: Windows-10
      device_type: "mokey"

targets:
  - name: Linux analyze
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      shard: analyze
      dependencies: >-
        [
          {"dependency": "ktlint", "version": "version_1_5_0"}
        ]
      tags: >
        ["framework","hostonly","shard","linux"]

  # This is a benchmark that does not require an attached device. However, we
  # are intentionally running it in the devicelab to ensure that it does not
  # run on a VM in order to avoid noisy results from the benchmark.
  - name: Linux analyzer_benchmark
    bringup: true # Flaky https://github.com/flutter/flutter/issues/161306
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      os: Linux
      device_type: "mokey"
      test_timeout_secs: "3600" # 1 hour
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "curl", "version": "version:7.64.0"}
        ]
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: analyzer_benchmark

  - name: Linux coverage
    bringup: true # https://github.com/flutter/flutter/issues/164591
    presubmit: false
    recipe: flutter/coverage
    timeout: 120
    enabled_branches:
      # Don't run this on release branches
      - master
    properties:
      tags: >
        ["framework", "hostonly", "shard", "linux"]

  - name: Linux packages_autoroller
    presubmit: false
    recipe: pub_autoroller/pub_autoroller
    # This takes a while because we need to fetch network dependencies and run
    # Gradle for every android app in the repo
    timeout: 45
    enabled_branches:
      # Don't run this on release branches
      - master
    properties:
      tags: >
        ["framework","hostonly","linux"]
      # Requires Android SDK since we may re-generate Gradle lockfiles
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "gh_cli", "version": "version:2.8.0-2-g32256d38"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]

  - name: Linux_android_emu android views
    recipe: devicelab/devicelab_drone
    properties:
      tags: >
        ["framework","hostonly","linux"]
      task_name: android_views
      presubmit_max_attempts: "2"
    timeout: 60

  - name: Linux build_aar_module_test
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      tags: >
        ["devicelab","hostonly"]
      task_name: build_aar_module_test
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux build_tests_1_5
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "clang", "version": "git_revision:5d5aba78dbbee75508f01bcaa69aedb2ab79065a"},
          {"dependency": "cmake", "version": "build_id:8787856497187628321"},
          {"dependency": "ninja", "version": "version:1.9.0"}
        ]
      shard: build_tests
      subshard: "1_5"
      tags: >
        ["framework", "hostonly", "shard", "linux"]

  - name: Linux build_tests_2_5
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "clang", "version": "git_revision:5d5aba78dbbee75508f01bcaa69aedb2ab79065a"},
          {"dependency": "cmake", "version": "build_id:8787856497187628321"},
          {"dependency": "ninja", "version": "version:1.9.0"}
        ]
      shard: build_tests
      subshard: "2_5"
      tags: >
        ["framework", "hostonly", "shard", "linux"]

  - name: Linux build_tests_3_5
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "clang", "version": "git_revision:5d5aba78dbbee75508f01bcaa69aedb2ab79065a"},
          {"dependency": "cmake", "version": "build_id:8787856497187628321"},
          {"dependency": "ninja", "version": "version:1.9.0"}
        ]
      shard: build_tests
      subshard: "3_5"
      tags: >
        ["framework", "hostonly", "shard", "linux"]

  - name: Linux build_tests_4_5
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "clang", "version": "git_revision:5d5aba78dbbee75508f01bcaa69aedb2ab79065a"},
          {"dependency": "cmake", "version": "build_id:8787856497187628321"},
          {"dependency": "ninja", "version": "version:1.9.0"}
        ]
      shard: build_tests
      subshard: "4_5"
      tags: >
        ["framework", "hostonly", "shard", "linux"]

  - name: Linux build_tests_5_5
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "clang", "version": "git_revision:5d5aba78dbbee75508f01bcaa69aedb2ab79065a"},
          {"dependency": "cmake", "version": "build_id:8787856497187628321"},
          {"dependency": "ninja", "version": "version:1.9.0"}
        ]
      shard: build_tests
      subshard: "5_5"
      tags: >
        ["framework", "hostonly", "shard", "linux"]

  - name: Linux ci_yaml flutter roller
    recipe: infra/ci_yaml
    presubmit: false
    timeout: 30
    enabled_branches:
      # Don't run this on release branches
      - master
    properties:
      tags: >
        ["framework", "hostonly", "shard", "linux"]
    runIf:
      - .ci.yaml
      - DEPS
      - engine/**

  - name: Linux customer_testing
    # This really just runs dev/bots/customer_testing/ci.sh,
    # but it does so indirectly via the flutter_drone recipe
    # calling the dev/bots/test.dart script.
    enabled_branches:
      - master
    recipe: flutter/flutter_drone
    # Timeout in minutes for the whole task.
    timeout: 60
    properties:
      shard: customer_testing
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      # TODO(flutter/flutter#164140): Reduce timeout once customer
      # tests are sharded.
      test_timeout_secs: "3600" # Allows 60 minutes (up from 30 default)

  # Despite the name, this really means "generate api_docs.zip", and "upload zip
  # to GCS", and conditionally on the "master" channel will also deploy the docs
  # to Firebase (https://main-api.flutter.dev/).
  #
  # See "Linux docs_generate_release" for how docs are built (but not published)
  # and "Linux docs_deploy_stable" for how docs deployed for the stable branch
  # (we do not deploy docs for beta).
  - name: Linux docs_publish
    recipe: flutter/docs
    presubmit: false
    backfill: false
    enabled_branches:
      - master
    timeout: 60
    dimensions:
      os: "Linux"
    properties:
      cores: "32"
      dependencies: >-
        [
          {"dependency": "dashing", "version": "0.4.0"},
          {"dependency": "firebase", "version": "v11.0.1"}
        ]
      tags: >
        ["framework", "hostonly", "linux"]
      validation: docs
      validation_name: Docs
      firebase_project: main-docs-flutter-prod
      release_ref: refs/heads/master
    drone_dimensions:
      - os=Linux

  - name: Linux docs_generate_release
    recipe: flutter/docs
    # TODO(matanlurey): This has no effect, is used to allow creating a builder.
    # Remove in the next PR.
    # https://github.com/flutter/flutter/issues/168913
    bringup: true
    scheduler: release
    presubmit: false
    postsubmit: false
    enabled_branches:
      - flutter-\d+\.\d+-candidate\.\d+
    timeout: 60
    dimensions:
      os: "Linux"
    properties:
      cores: "32"
      dependencies: >-
        [
          {"dependency": "dashing", "version": "0.4.0"},
          {"dependency": "firebase", "version": "v11.0.1"}
        ]
      tags: >
        ["framework", "hostonly", "linux"]
      validation: docs
      validation_name: Docs
      # TODO(matanlurey): Neither of these properties are actually used, since
      # the branch name is not "master", but if they are removed, the recipe
      # will fail. See https://github.com/flutter/flutter/issues/169108.
      firebase_project: main-docs-flutter-prod
      release_ref: refs/heads/master
    drone_dimensions:
      - os=Linux


  # This step runs on the release channel "stable", after the same commit SHA
  # has been run and built by Linux flutter_release_builder as part of a release
  # candidate branch (i.e. /flutter-\d+\.\d+-candidate\.\d+/) in the previous
  # target, "Linux docs_generate_release".
  - name: Linux docs_deploy_stable
    recipe: flutter/docs
    scheduler: release
    presubmit: false
    postsubmit: false
    enabled_branches:
      - stable
    timeout: 60
    properties:
      cores: "32"
      dependencies: >-
        [
          {"dependency": "dashing", "version": "0.4.0"},
          {"dependency": "firebase", "version": "v11.0.1"}
        ]
      tags: >
        ["framework", "hostonly", "linux"]
      validation: docs_deploy
      validation_name: Docs_deploy
      firebase_project: docs-flutter-dev
    drone_dimensions:
      - os=Linux

  - name: Linux docs_test
    recipe: flutter/flutter_drone
    timeout: 90 # https://github.com/flutter/flutter/issues/120901
    properties:
      cores: "32"
      dependencies: >-
        [
          {"dependency": "dashing", "version": "0.4.0"}
        ]
      firebase_project: ""
      release_ref: ""
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      shard: docs
    runIf:
      - bin/**
      - dev/**
      - packages/flutter/**
      - packages/flutter_drive/**
      - packages/flutter_localizations/**
      - packages/flutter_test/**
      - packages/flutter_web_plugins/**
      - packages/integration_test/**
      - .ci.yaml
      - engine/**
      - DEPS
      - dartdoc_options.yaml

  - name: Linux engine_dependency_proxy_test
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      tags: >
        ["devicelab", "hostonly", "linux"]
      task_name: engine_dependency_proxy_test
    runIf:
      - dev/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux firebase_release_smoke_test
    recipe: firebaselab/firebaselab
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      tags: >
        ["firebaselab"]
      task_name: release_smoke_test
      physical_devices: >-
        [
          "--device", "model=shiba,version=34",
          "--device", "model=redfin,version=30",
          "--device", "model=griffin,version=24"
        ]
      # TODO(flutter/flutter#123331): This device is flaking.
      # "--device", "model=Nexus6P,version=25"
      virtual_devices: >-
        [
          "--device", "model=Nexus5.gce_x86,version=21",
          "--device", "model=Nexus5.gce_x86,version=22",
          "--device", "model=Nexus5.gce_x86,version=23",
          "--device", "model=Nexus6P,version=25",
          "--device", "model=Nexus6P,version=26",
          "--device", "model=Nexus6P,version=27",
          "--device", "model=NexusLowRes,version=29"
        ]

  - name: Linux flutter_packaging_test
    recipe: packaging/packaging
    presubmit: false
    enabled_branches:
      - master
    timeout: 60 # TODO(https://github.com/flutter/flutter/issues/162654)
    properties:
      task_name: flutter_packaging
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      test_timeout_secs: "3600" # TODO(https://github.com/flutter/flutter/issues/162654)
    runIf:
      - .ci.yaml
      - engine/**
      - DEPS
      - dev/bots/**

  - name: Linux flutter_plugins
    recipe: flutter/flutter_drone
    enabled_branches:
      - master
    timeout: 60
    properties:
      shard: flutter_plugins
      subshard: analyze
      tags: >
        ["framework", "hostonly", "shard", "linux"]

  - name: Linux framework_tests_libraries
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: framework_tests
      subshard: libraries
      tags: >
        ["framework","hostonly","shard", "linux"]
    runIf:
      - dev/**
      - packages/flutter/**
      - packages/flutter_driver/**
      - packages/integration_test/**
      - packages/flutter_localizations/**
      - packages/fuchsia_remote_debug_protocol/**
      - packages/flutter_test/**
      - packages/flutter_goldens/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux framework_tests_slow
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      shard: framework_tests
      subshard: slow
      tags: >
        ["framework", "hostonly", "shard", "linux"]
    runIf:
      - dev/**
      - packages/flutter/**
      - packages/flutter_driver/**
      - packages/integration_test/**
      - packages/flutter_localizations/**
      - packages/fuchsia_remote_debug_protocol/**
      - packages/flutter_test/**
      - packages/flutter_goldens/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux framework_tests_misc
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "clang", "version": "git_revision:5d5aba78dbbee75508f01bcaa69aedb2ab79065a"},
          {"dependency": "cmake", "version": "build_id:8787856497187628321"},
          {"dependency": "ninja", "version": "version:1.9.0"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "android_sdk", "version": "version:35v1"}
        ]
      shard: framework_tests
      subshard: misc
      tags: >
        ["framework", "hostonly", "shard", "linux"]
    runIf:
      - dev/**
      - examples/api/**
      - packages/flutter/**
      - packages/flutter_driver/**
      - packages/integration_test/**
      - packages/flutter_localizations/**
      - packages/fuchsia_remote_debug_protocol/**
      - packages/flutter_test/**
      - packages/flutter_goldens/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux framework_tests_widgets
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: framework_tests
      subshard: widgets
      tags: >
        ["framework","hostonly","shard", "linux"]
    runIf:
      - dev/**
      - packages/flutter/**
      - packages/flutter_driver/**
      - packages/integration_test/**
      - packages/flutter_localizations/**
      - packages/fuchsia_remote_debug_protocol/**
      - packages/flutter_test/**
      - packages/flutter_goldens/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux fuchsia_precache
    recipe: flutter/flutter_drone
    timeout: 60
    enabled_branches:
      # Don't run this on release branches
      - master
    presubmit: false
    properties:
      shard: fuchsia_precache
      tags: >
        ["framework", "hostonly", "shard", "linux"]
    runIf:
      - engine/**
      - DEPS
      - .ci.yaml

  - name: Linux gradle_desugar_classes_test
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      tags: >
        ["devicelab", "hostonly", "linux"]
      task_name: gradle_desugar_classes_test
    runIf:
      - dev/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux gradle_java8_compile_test
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      tags: >
        ["devicelab", "hostonly", "linux"]
      task_name: gradle_java8_compile_test
    runIf:
      - dev/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux gradle_plugin_bundle_test
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      tags: >
        ["devicelab", "hostonly", "linux"]
      task_name: gradle_plugin_bundle_test
    runIf:
      - dev/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux gradle_plugin_fat_apk_test
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      tags: >
        ["devicelab", "hostonly", "linux"]
      task_name: gradle_plugin_fat_apk_test
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux gradle_plugin_light_apk_test
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      tags: >
        ["devicelab", "hostonly", "linux"]
      task_name: gradle_plugin_light_apk_test
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux module_custom_host_app_name_test
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      tags: >
        ["devicelab", "hostonly", "linux"]
      task_name: module_custom_host_app_name_test
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux module_host_with_custom_build_test
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      tags: >
        ["devicelab", "hostonly", "linux"]
      task_name: module_host_with_custom_build_test
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux build_android_host_app_with_module_aar
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      tags: >
        ["devicelab", "hostonly", "linux"]
      task_name: build_android_host_app_with_module_aar
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux build_android_host_app_with_module_source
    recipe: devicelab/devicelab_drone
    timeout: 60
    presubmit: false
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      tags: >
        ["devicelab", "hostonly", "linux"]
      task_name: build_android_host_app_with_module_source
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux plugin_dependencies_test
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      tags: >
        ["devicelab", "hostonly", "linux"]
      task_name: plugin_dependencies_test
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux plugin_test
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      # TODO(fujino): delete once propagation from
      # https://github.com/flutter/flutter/issues/158521 completes.
      drone_dimensions: >
        ["os=Linux", "os=Ubuntu-20"]
      tags: >
        ["devicelab", "hostonly", "linux"]
      task_name: plugin_test
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux plugin_test_linux
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "clang", "version": "git_revision:5d5aba78dbbee75508f01bcaa69aedb2ab79065a"},
          {"dependency": "cmake", "version": "build_id:8787856497187628321"},
          {"dependency": "ninja", "version": "version:1.9.0"},
          {"dependency": "curl", "version": "version:7.64.0"}
        ]
      tags: >
        ["devicelab", "hostonly", "linux"]
      task_name: plugin_test_linux
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux run_debug_test_linux
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      xvfb: "1"
      dependencies: >-
        [
          {"dependency": "clang", "version": "git_revision:5d5aba78dbbee75508f01bcaa69aedb2ab79065a"},
          {"dependency": "cmake", "version": "build_id:8787856497187628321"},
          {"dependency": "ninja", "version": "version:1.9.0"}
        ]
      tags: >
        ["devicelab", "hostonly", "linux"]
      task_name: run_debug_test_linux
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux linux_desktop_impeller
    recipe: devicelab/devicelab_drone
    timeout: 60
    presubmit: false
    properties:
      xvfb: "1"
      dependencies: >-
        [
          {"dependency": "clang", "version": "git_revision:5d5aba78dbbee75508f01bcaa69aedb2ab79065a"},
          {"dependency": "cmake", "version": "build_id:8787856497187628321"},
          {"dependency": "ninja", "version": "version:1.9.0"}
        ]
      tags: >
        ["devicelab", "hostonly", "linux"]
      task_name: linux_desktop_impeller

  - name: Linux_android_emu android_display_cutout
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      tags: >
        ["devicelab", "linux"]
      task_name: android_display_cutout
      presubmit_max_attempts: "2"

  - name: Linux android_release_builds_exclude_dev_dependencies_test
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      tags: >
        ["devicelab", "hostonly", "linux"]
      task_name: android_release_builds_exclude_dev_dependencies_test
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux run_release_test_linux
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      xvfb: "1"
      dependencies: >-
        [
          {"dependency": "clang", "version": "git_revision:5d5aba78dbbee75508f01bcaa69aedb2ab79065a"},
          {"dependency": "cmake", "version": "build_id:8787856497187628321"},
          {"dependency": "ninja", "version": "version:1.9.0"}
        ]
      tags: >
        ["devicelab", "hostonly", "linux"]
      task_name: run_release_test_linux
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux skp_generator
    enabled_branches:
      - main
      - master
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      shard: skp_generator
      subshard: "0"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
    runIf:
      - dev/**
      - packages/flutter/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux technical_debt__cost
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"}
        ]
      tags: >
        ["devicelab", "hostonly", "linux"]
      task_name: technical_debt__cost

  - name: Linux test_ownership
    recipe: infra/test_ownership
    enabled_branches:
      - main
      - master
    properties:
      tags: >
        ["framework", "hostonly", "shard", "linux"]
    runIf:
      - engine/**
      - DEPS
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux tool_integration_tests_1_7
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "clang", "version": "git_revision:5d5aba78dbbee75508f01bcaa69aedb2ab79065a"},
          {"dependency": "cmake", "version": "build_id:8787856497187628321"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "ninja", "version": "version:1.9.0"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      shard: tool_integration_tests
      subshard: "1_7"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      test_timeout_secs: "2700"
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux tool_integration_tests_2_7
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "clang", "version": "git_revision:5d5aba78dbbee75508f01bcaa69aedb2ab79065a"},
          {"dependency": "cmake", "version": "build_id:8787856497187628321"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "ninja", "version": "version:1.9.0"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      shard: tool_integration_tests
      subshard: "2_7"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      test_timeout_secs: "2700"
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux tool_integration_tests_3_7
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "clang", "version": "git_revision:5d5aba78dbbee75508f01bcaa69aedb2ab79065a"},
          {"dependency": "cmake", "version": "build_id:8787856497187628321"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "ninja", "version": "version:1.9.0"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      shard: tool_integration_tests
      subshard: "3_7"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      test_timeout_secs: "2700"
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux tool_integration_tests_4_7
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "clang", "version": "git_revision:5d5aba78dbbee75508f01bcaa69aedb2ab79065a"},
          {"dependency": "cmake", "version": "build_id:8787856497187628321"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "ninja", "version": "version:1.9.0"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      shard: tool_integration_tests
      subshard: "4_7"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      test_timeout_secs: "2700"
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux tool_integration_tests_5_7
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "clang", "version": "git_revision:5d5aba78dbbee75508f01bcaa69aedb2ab79065a"},
          {"dependency": "cmake", "version": "build_id:8787856497187628321"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "ninja", "version": "version:1.9.0"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      shard: tool_integration_tests
      subshard: "5_7"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      test_timeout_secs: "2700"
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux tool_integration_tests_6_7
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "clang", "version": "git_revision:5d5aba78dbbee75508f01bcaa69aedb2ab79065a"},
          {"dependency": "cmake", "version": "build_id:8787856497187628321"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "ninja", "version": "version:1.9.0"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      shard: tool_integration_tests
      subshard: "6_7"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      test_timeout_secs: "2700"
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux tool_integration_tests_7_7
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "clang", "version": "git_revision:5d5aba78dbbee75508f01bcaa69aedb2ab79065a"},
          {"dependency": "cmake", "version": "build_id:8787856497187628321"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "ninja", "version": "version:1.9.0"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      shard: tool_integration_tests
      subshard: "7_7"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      test_timeout_secs: "2700"
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux android_preview_tool_integration_tests
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      # This makes use of UpsideDownCake, a preview version of android. Preview versions eventually
      # get removed from the sdk manager, so it is hosted on CIPD to ensure integration testing
      # doesn't flake when that happens.
      # https://chrome-infra-packages.appspot.com/p/flutter/android/sdk/all/linux-amd64/+/version:udcv1
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:udcv1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "clang", "version": "git_revision:5d5aba78dbbee75508f01bcaa69aedb2ab79065a"},
          {"dependency": "cmake", "version": "build_id:8787856497187628321"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "ninja", "version": "version:1.9.0"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      shard: android_preview_tool_integration_tests
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      test_timeout_secs: "2700"
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux android_java11_tool_integration_tests
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "clang", "version": "git_revision:5d5aba78dbbee75508f01bcaa69aedb2ab79065a"},
          {"dependency": "cmake", "version": "build_id:8787856497187628321"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "ninja", "version": "version:1.9.0"},
          {"dependency": "open_jdk", "version": "version:11"}
        ]
      shard: android_java11_tool_integration_tests
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      test_timeout_secs: "2700"
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux android_java11_dependency_smoke_tests
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:11"}
        ]
      task_name: android_java11_dependency_smoke_tests
      tags: >
        ["devicelab", "hostonly", "linux"]
      test_timeout_secs: "2700"
    runIf:
      - packages/flutter_tools/templates/**
      - packages/flutter_tools/gradle/**
      - .ci.yaml
      - engine/**
      - DEPS
      - dev/devicelab/bin/tasks/android_java11_dependency_smoke_tests.dart
      - dev/devicelab/lib/framework/dependency_smoke_test_task_definition.dart

  - name: Linux android_java17_dependency_smoke_tests
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:17"}
        ]
      task_name: android_java17_dependency_smoke_tests
      tags: >
        ["devicelab", "hostonly", "linux"]
      test_timeout_secs: "2700"
    runIf:
      - packages/flutter_tools/templates/**
      - packages/flutter_tools/gradle/**
      - .ci.yaml
      - engine/**
      - DEPS
      - dev/devicelab/bin/tasks/android_java17_dependency_smoke_tests.dart
      - dev/devicelab/lib/framework/dependency_smoke_test_task_definition.dart

  - name: Linux tool_tests_commands
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "clang", "version": "git_revision:5d5aba78dbbee75508f01bcaa69aedb2ab79065a"},
          {"dependency": "cmake", "version": "build_id:8787856497187628321"},
          {"dependency": "ninja", "version": "version:1.9.0"}
        ]
      shard: tool_tests
      subshard: commands
      tags: >
        ["framework", "hostonly", "shard", "linux"]
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux tool_tests_general
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      shard: tool_tests
      subshard: general
      tags: >
        ["framework", "hostonly", "shard", "linux"]
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux tool_tests_widget_preview_scaffold
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      shard: tool_tests
      subshard: widget_preview_scaffold
      tags: >
        ["framework", "hostonly", "shard", "linux"]
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux_android_emu android_engine_vulkan_tests
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      shard: android_engine_vulkan_tests
      tags: >
         ["framework", "hostonly", "shard", "linux"]
      dependencies: >-
        [
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]

  - name: Linux_mokey android_engine_vulkan_tests
    recipe: flutter/flutter_drone
    # TODO(matanlurey): https://github.com/flutter/flutter/issues/163025.
    bringup: true
    timeout: 60
    properties:
      shard: android_engine_vulkan_tests
      tags: >
         ["framework", "hostonly", "shard", "linux"]
      dependencies: >-
        [
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]

  - name: Linux_android_emu android_engine_opengles_tests
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      shard: android_engine_opengles_tests
      tags: >
         ["framework", "hostonly", "shard", "linux"]
      dependencies: >-
        [
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]

  - name: Linux web_benchmarks_canvaskit
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"}
        ]
      tags: >
        ["devicelab","hostonly", "linux"]
      task_name: web_benchmarks_canvaskit

  - name: Linux web_benchmarks_skwasm
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"}
        ]
      tags: >
        ["devicelab"]
      task_name: web_benchmarks_skwasm
    runIf:
      - dev/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux web_benchmarks_skwasm_st
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"}
        ]
      tags: >
        ["devicelab"]
      task_name: web_benchmarks_skwasm_st
    runIf:
      - dev/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux web_long_running_tests_1_5
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: web_long_running_tests
      subshard: "1_5"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      # Retry for flakes caused by https://github.com/flutter/flutter/issues/132654
      presubmit_max_attempts: "2"
    runIf:
      - dev/**
      - packages/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux web_long_running_tests_2_5
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: web_long_running_tests
      subshard: "2_5"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      # Retry for flakes caused by https://github.com/flutter/flutter/issues/132654
      presubmit_max_attempts: "2"
    runIf:
      - dev/**
      - packages/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux web_long_running_tests_3_5
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: web_long_running_tests
      subshard: "3_5"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      # Retry for flakes caused by https://github.com/flutter/flutter/issues/132654
      presubmit_max_attempts: "2"
    runIf:
      - dev/**
      - packages/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux web_long_running_tests_4_5
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: web_long_running_tests
      subshard: "4_5"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      # Retry for flakes caused by https://github.com/flutter/flutter/issues/132654
      presubmit_max_attempts: "2"
    runIf:
      - dev/**
      - packages/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux web_long_running_tests_5_5
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: web_long_running_tests
      subshard: "5_5"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      # Retry for flakes caused by https://github.com/flutter/flutter/issues/132654
      presubmit_max_attempts: "2"
    runIf:
      - dev/**
      - packages/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux web_canvaskit_tests_0
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: web_canvaskit_tests
      subshard: "0"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      # Retry for flakes caused by https://github.com/flutter/flutter/issues/132654
      presubmit_max_attempts: "2"
    runIf:
      - dev/**
      - packages/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux web_canvaskit_tests_1
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: web_canvaskit_tests
      subshard: "1"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      # Retry for flakes caused by https://github.com/flutter/flutter/issues/132654
      presubmit_max_attempts: "2"
    runIf:
      - dev/**
      - packages/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux web_canvaskit_tests_2
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: web_canvaskit_tests
      subshard: "2"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      # Retry for flakes caused by https://github.com/flutter/flutter/issues/132654
      presubmit_max_attempts: "2"
    runIf:
      - dev/**
      - packages/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux web_canvaskit_tests_3
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: web_canvaskit_tests
      subshard: "3"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      # Retry for flakes caused by https://github.com/flutter/flutter/issues/132654
      presubmit_max_attempts: "2"
    runIf:
      - dev/**
      - packages/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux web_canvaskit_tests_4
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: web_canvaskit_tests
      subshard: "4"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      # Retry for flakes caused by https://github.com/flutter/flutter/issues/132654
      presubmit_max_attempts: "2"
    runIf:
      - dev/**
      - packages/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux web_canvaskit_tests_5
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: web_canvaskit_tests
      subshard: "5"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      # Retry for flakes caused by https://github.com/flutter/flutter/issues/132654
      presubmit_max_attempts: "2"
    runIf:
      - dev/**
      - packages/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux web_canvaskit_tests_6
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: web_canvaskit_tests
      subshard: "6"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      # Retry for flakes caused by https://github.com/flutter/flutter/issues/132654
      presubmit_max_attempts: "2"
    runIf:
      - dev/**
      - packages/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux web_canvaskit_tests_7_last
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: web_canvaskit_tests
      subshard: "7_last"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      # Retry for flakes caused by https://github.com/flutter/flutter/issues/132654
      presubmit_max_attempts: "2"
    runIf:
      - dev/**
      - packages/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux web_skwasm_tests_0
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: web_skwasm_tests
      subshard: "0"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      # Retry for flakes caused by https://github.com/flutter/flutter/issues/132654
      presubmit_max_attempts: "2"
    runIf:
      - dev/**
      - packages/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux web_skwasm_tests_1
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: web_skwasm_tests
      subshard: "1"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      # Retry for flakes caused by https://github.com/flutter/flutter/issues/132654
      presubmit_max_attempts: "2"
    runIf:
      - dev/**
      - packages/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux web_skwasm_tests_2
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: web_skwasm_tests
      subshard: "2"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      # Retry for flakes caused by https://github.com/flutter/flutter/issues/132654
      presubmit_max_attempts: "2"
    runIf:
      - dev/**
      - packages/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux web_skwasm_tests_3
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: web_skwasm_tests
      subshard: "3"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      # Retry for flakes caused by https://github.com/flutter/flutter/issues/132654
      presubmit_max_attempts: "2"
    runIf:
      - dev/**
      - packages/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux web_skwasm_tests_4
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: web_skwasm_tests
      subshard: "4"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      # Retry for flakes caused by https://github.com/flutter/flutter/issues/132654
      presubmit_max_attempts: "2"
    runIf:
      - dev/**
      - packages/**
      - bin/**
      - .ci.yaml
      - DEPS
      - engine/**

  - name: Linux web_skwasm_tests_5
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: web_skwasm_tests
      subshard: "5"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      # Retry for flakes caused by https://github.com/flutter/flutter/issues/132654
      presubmit_max_attempts: "2"
    runIf:
      - dev/**
      - packages/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux web_skwasm_tests_6
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: web_skwasm_tests
      subshard: "6"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      # Retry for flakes caused by https://github.com/flutter/flutter/issues/132654
      presubmit_max_attempts: "2"
    runIf:
      - dev/**
      - packages/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux web_skwasm_tests_7_last
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: web_skwasm_tests
      subshard: "7_last"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      # Retry for flakes caused by https://github.com/flutter/flutter/issues/132654
      presubmit_max_attempts: "2"
    runIf:
      - dev/**
      - packages/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux web_tool_tests
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: web_tool_tests
      subshard: "1_1"
      tags: >
        ["framework", "hostonly", "shard", "linux"]
      # Retry for flakes caused by https://github.com/flutter/flutter/issues/132654
      presubmit_max_attempts: "2"
      test_timeout_secs: "3600" # https://github.com/flutter/flutter/issues/162714
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Linux_android_emu android_defines_test
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      tags: >
        ["devicelab", "linux"]
      task_name: android_defines_test
      presubmit_max_attempts: "2"

  - name: Linux_android_emu_unstable android_defines_test
    recipe: devicelab/devicelab_drone
    timeout: 60
    bringup: true
    properties:
      tags: >
        ["devicelab", "linux"]
      task_name: android_defines_test

  - name: Linux_pixel_7pro android_obfuscate_test
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: android_obfuscate_test

  - name: Linux_pixel_7pro android_semantics_integration_test
    recipe: devicelab/devicelab_drone
    bringup: true # Failing: https://github.com/flutter/flutter/issues/153594
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: android_semantics_integration_test

  # linux mokey benchmark
  - name: Linux_mokey android_view_scroll_perf__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: android_view_scroll_perf__timeline_summary

  # linux mokey benchmark
  - name: Linux_mokey animated_image_gc_perf
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: animated_image_gc_perf

  # linux mokey benchmark
  - name: Linux_mokey animated_complex_opacity_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: animated_complex_opacity_perf__e2e_summary

  # linux mokey benchmark
  - name: Linux_mokey animated_complex_image_filtered_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: animated_complex_image_filtered_perf__e2e_summary

  # linux mokey benchmark
  - name: Linux_mokey animated_placeholder_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: animated_placeholder_perf__e2e_summary

  # linux mokey benchmark
  - name: Linux_mokey backdrop_filter_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: backdrop_filter_perf__e2e_summary

  - name: Linux_pixel_7pro backdrop_filter_perf__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: backdrop_filter_perf__timeline_summary

  # Uses Impeller.
  - name: Linux_pixel_7pro draw_atlas_perf_opengles__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: draw_atlas_perf_opengles__timeline_summary

  # Uses Impeller.
  - name: Linux_pixel_7pro draw_atlas_perf__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: draw_atlas_perf__timeline_summary

  # Uses Impeller.
  - name: Linux_pixel_7pro dynamic_path_tessellation_perf__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: dynamic_path_tessellation_perf__timeline_summary

  # Uses Impeller.
  - name: Linux_pixel_7pro static_path_tessellation_perf__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: static_path_tessellation_perf__timeline_summary

  # Uses Impeller.
  - name: Linux_pixel_7pro hello_world_impeller
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: hello_world_impeller

  - name: Linux_pixel_7pro basic_material_app_android__compile
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: basic_material_app_android__compile

  - name: Linux_pixel_7pro channels_integration_test
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: channels_integration_test

  # linux mokey benchmark
  - name: Linux_mokey clipper_cache_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab","android","linux","mokey"]
      task_name: clipper_cache_perf__e2e_summary

  # linux mokey benchmark
  - name: Linux_mokey color_filter_and_fade_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: color_filter_and_fade_perf__e2e_summary

  # linux mokey benchmark
  - name: Linux_mokey color_filter_cache_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: color_filter_cache_perf__e2e_summary

  # linux mokey benchmark
  - name: Linux_mokey color_filter_with_unstable_child_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab","android","linux","mokey"]
      task_name: color_filter_with_unstable_child_perf__e2e_summary

  # linux mokey benchmark
  - name: Linux_mokey shader_mask_cache_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: shader_mask_cache_perf__e2e_summary

  # linux mokey benchmark
  - name: Linux_mokey complex_layout_android__scroll_smoothness
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: complex_layout_android__scroll_smoothness
      dependencies: >-
        [
          {"dependency": "open_jdk", "version": "version:21"}
        ]

  # linux mokey benchmark
  - name: Linux_mokey complex_layout_scroll_perf__devtools_memory
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab","android","linux","mokey"]
      task_name: complex_layout_scroll_perf__devtools_memory
      dependencies: >-
        [
          {"dependency": "open_jdk", "version": "version:21"}
        ]

  # linux mokey benchmark
  - name: Linux_mokey complex_layout_scroll_perf__memory
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab","android","linux","mokey"]
      task_name: complex_layout_scroll_perf__memory
      dependencies: >-
        [
          {"dependency": "open_jdk", "version": "version:21"}
        ]

  # linux mokey benchmark
  - name: Linux_mokey complex_layout_scroll_perf__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab","android","linux","mokey"]
      task_name: complex_layout_scroll_perf__timeline_summary
      dependencies: >-
        [
          {"dependency": "open_jdk", "version": "version:21"}
        ]

  - name: Linux_pixel_7pro complex_layout_scroll_perf__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: complex_layout_scroll_perf__timeline_summary
      dependencies: >-
        [
          {"dependency": "open_jdk", "version": "version:21"}
        ]

  - name: Linux_pixel_7pro complex_layout_scroll_perf_impeller__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: complex_layout_scroll_perf_impeller__timeline_summary
      dependencies: >-
        [
          {"dependency": "open_jdk", "version": "version:21"}
        ]

  - name: Linux_pixel_7pro complex_layout_scroll_perf_impeller_gles__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: complex_layout_scroll_perf_impeller_gles__timeline_summary
      dependencies: >-
        [
          {"dependency": "open_jdk", "version": "version:21"}
        ]

  # linux mokey benchmark
  - name: Linux_mokey complex_layout_semantics_perf
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: complex_layout_semantics_perf
      dependencies: >-
        [
          {"dependency": "open_jdk", "version": "version:21"}
        ]

  # linux mokey benchmark
  - name: Linux_mokey complex_layout__start_up
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: complex_layout__start_up
      dependencies: >-
        [
          {"dependency": "open_jdk", "version": "version:21"}
        ]

  # linux mokey benchmark
  - name: Linux_mokey cubic_bezier_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: cubic_bezier_perf__e2e_summary

  - name: Linux_pixel_7pro cubic_bezier_perf__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: cubic_bezier_perf__timeline_summary

  # linux mokey benchmark
  - name: Linux_mokey cull_opacity_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: cull_opacity_perf__e2e_summary

  - name: Linux_pixel_7pro cull_opacity_perf__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: cull_opacity_perf__timeline_summary

  # linux mokey benchmark
  - name: Linux_mokey devtools_profile_start_test
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: devtools_profile_start_test

  - name: Linux_pixel_7pro drive_perf_debug_warning
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: drive_perf_debug_warning

  - name: Linux_pixel_7pro embedded_android_views_integration_test
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: embedded_android_views_integration_test

  - name: Linux_android_emu external_textures_integration_test
    recipe: devicelab/devicelab_drone
    bringup: true
    timeout: 60
    # Functionally the same as "presubmit: false", except that we will run on
    # presubmit during engine rolls. This test is the *only* automated e2e
    # test for external textures for the engine, it should never break.
    runIf:
      - engine/**
      - DEPS
      - .ci.yaml
    properties:
      tags: >
        ["devicelab", "linux"]
      task_name: external_textures_integration_test
      presubmit_max_attempts: "2"

  # linux mokey benchmark
  - name: Linux_mokey fading_child_animation_perf__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux"]
      task_name: fading_child_animation_perf__timeline_summary

  # linux mokey benchmark
  - name: Linux_mokey fast_scroll_heavy_gridview__memory
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: fast_scroll_heavy_gridview__memory

  # linux mokey benchmark
  - name: Linux_mokey fast_scroll_large_images__memory
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: fast_scroll_large_images__memory

  - name: Linux_pixel_7pro flavors_test
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: flavors_test

  # linux mokey benchmark
  - name: Linux_mokey flutter_engine_group_performance
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: flutter_engine_group_performance

  # linux mokey benchmark
  - name: Linux_mokey flutter_gallery__back_button_memory
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: flutter_gallery__back_button_memory

  # linux mokey benchmark
  - name: Linux_mokey flutter_gallery__image_cache_memory
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: flutter_gallery__image_cache_memory

  # linux mokey benchmark
  - name: Linux_mokey flutter_gallery__memory_nav
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab" ,"android", "linux", "mokey"]
      task_name: flutter_gallery__memory_nav

  # linux mokey benchmark
  - name: Linux_mokey flutter_gallery__start_up
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux"]
      task_name: flutter_gallery__start_up

  # linux mokey benchmark
  - name: Linux_mokey flutter_gallery_lazy__start_up
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux"]
      task_name: flutter_gallery_lazy__start_up

  # linux mokey benchmark
  - name: Linux_mokey flutter_gallery__start_up_delayed
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: flutter_gallery__start_up_delayed

  - name: Linux_pixel_7pro flutter_gallery_android__compile
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: flutter_gallery_android__compile

  - name: Linux_pixel_7pro flutter_gallery_v2_chrome_run_test
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: flutter_gallery_v2_chrome_run_test

  - name: Linux flutter_gallery_v2_web_compile_test
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "hostonly", "linux"]
      task_name: flutter_gallery_v2_web_compile_test

  # linux mokey benchmark
  - name: Linux_mokey flutter_test_performance
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux"]
      task_name: flutter_test_performance

  # linux mokey benchmark
  - name: Linux_mokey flutter_view__start_up
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: flutter_view__start_up

  # linux mokey benchmark
  - name: Linux_mokey fullscreen_textfield_perf
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: fullscreen_textfield_perf

  # linux mokey benchmark
  - name: Linux_mokey fullscreen_textfield_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: fullscreen_textfield_perf__e2e_summary

  # linux mokey benchmark
  - name: Linux_mokey very_long_picture_scrolling_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 120
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: very_long_picture_scrolling_perf__e2e_summary

  # linux mokey benchmark
  - name: Linux_mokey hello_world__memory
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: hello_world__memory

  # linux mokey benchmark
  - name: Linux_mokey home_scroll_perf__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: home_scroll_perf__timeline_summary

  # linux mokey benchmark
  - name: Linux_mokey hot_mode_dev_cycle_linux__benchmark
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: hot_mode_dev_cycle_linux__benchmark
    runIf:
      - .ci.yaml
      - dev/**
      - DEPS
      - engine/**

  # linux mokey test
  - name: Linux_mokey hybrid_android_views_integration_test
    recipe: devicelab/devicelab_drone
    presubmit: false
    bringup: true # https://github.com/flutter/flutter/issues/148085
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: hybrid_android_views_integration_test

  # linux mokey benchmark
  - name: Linux_mokey image_list_jit_reported_duration
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: image_list_jit_reported_duration

  # linux mokey benchmark
  - name: Linux_mokey imagefiltered_transform_animation_perf__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: imagefiltered_transform_animation_perf__timeline_summary

  - name: Linux_pixel_7pro imagefiltered_transform_animation_perf__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: imagefiltered_transform_animation_perf__timeline_summary

  # linux mokey benchmark
  - name: Linux_mokey image_list_reported_duration
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: image_list_reported_duration

  - name: Linux_pixel_7pro integration_ui_driver
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: integration_ui_driver

  - name: Linux_pixel_7pro integration_ui_keyboard_resize
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: integration_ui_keyboard_resize

  - name: Linux_pixel_7pro integration_ui_textfield
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: integration_ui_textfield

  # linux mokey benchmark
  - name: Linux_mokey large_image_changer_perf_android
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: large_image_changer_perf_android

  - name: Linux_pixel_7pro linux_chrome_dev_mode
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: linux_chrome_dev_mode

  # linux mokey benchmark
  - name: Linux_mokey multi_widget_construction_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: multi_widget_construction_perf__e2e_summary

  # linux mokey benchmark
  - name: Linux_mokey list_text_layout_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: list_text_layout_perf__e2e_summary

  # linux mokey benchmark
  - name: Linux_mokey list_text_layout_impeller_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      ignore_flakiness: "true"
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: list_text_layout_impeller_perf__e2e_summary

  - name: Linux_pixel_7pro native_assets_android
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: native_assets_android

  # linux mokey benchmark
  - name: Linux_mokey new_gallery__crane_perf
    bringup: true # Flaky https://github.com/flutter/flutter/issues/165963
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: new_gallery__crane_perf

  # linux mokey benchmark
  - name: Linux_mokey old_gallery__transition_perf
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: old_gallery__transition_perf

  - name: Linux_build_test flutter_gallery__transition_perf
    recipe: devicelab/devicelab_drone_build_test
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: flutter_gallery__transition_perf
      artifact: gallery__transition_perf
      drone_dimensions: >
        ["device_os=U","os=Linux", "device_type=mokey"]

  - name: Linux_build_test flutter_gallery__transition_perf_e2e
    recipe: devicelab/devicelab_drone_build_test
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: flutter_gallery__transition_perf_e2e
      artifact: gallery__transition_perf_e2e
      drone_dimensions: >
        ["device_os=U","os=Linux", "device_type=mokey"]

  - name: Linux_build_test flutter_gallery__transition_perf_hybrid
    recipe: devicelab/devicelab_drone_build_test
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: flutter_gallery__transition_perf_hybrid
      artifact: gallery__transition_perf_hybrid
      drone_dimensions: >
        ["device_os=U","os=Linux", "device_type=mokey"]

  # linux mokey benchmark
  - name: Linux_mokey flutter_gallery__transition_perf_with_semantics
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: flutter_gallery__transition_perf_with_semantics

  # Mokey, Impeller
  - name: Linux_mokey new_gallery_impeller__transition_perf
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: new_gallery_impeller__transition_perf

  # Mokey, Impeller (OpenGL)
  - name: Linux_mokey new_gallery_opengles_impeller__transition_perf
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: new_gallery_opengles_impeller__transition_perf

  # Mokey, Skia
  - name: Linux_mokey new_gallery__transition_perf
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: new_gallery__transition_perf

  # Pixel 7 Pro, Skia
  - name: Linux_pixel_7pro new_gallery__transition_perf
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: new_gallery__transition_perf

  # Pixel 7 Pro, Impeller (Vulkan)
  - name: Linux_pixel_7pro new_gallery_impeller_old_zoom__transition_perf
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: new_gallery_impeller_old_zoom__transition_perf

  # Pixel 7 Pro, Impeller (Vulkan)
  - name: Linux_pixel_7pro new_gallery_impeller__transition_perf
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: new_gallery_impeller__transition_perf

  # Samsung Galaxy S24, Impeller (Vulkan)
  - name: Linux_galaxy_s24 new_gallery_impeller__transition_perf
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "samsung", "s24"]
      task_name: new_gallery_impeller__transition_perf

  # Pixel 7 Pro, Impeller (OpenGL)
  - name: Linux_pixel_7pro new_gallery_opengles_impeller__transition_perf
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: new_gallery_opengles_impeller__transition_perf

  # linux mokey benchmark
  - name: Linux_mokey picture_cache_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: picture_cache_perf__e2e_summary

  - name: Linux_pixel_7pro picture_cache_perf__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: picture_cache_perf__timeline_summary

  # linux mokey benchmark
  - name: Linux_mokey android_picture_cache_complexity_scoring_perf__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: android_picture_cache_complexity_scoring_perf__timeline_summary

  # linux mokey benchmark
  - name: Linux_mokey slider_perf_android
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: slider_perf_android

  # linux mokey benchmark
  - name: Linux_mokey platform_channels_benchmarks
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: platform_channels_benchmarks

  - name: Linux_pixel_7pro platform_channels_benchmarks
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: platform_channels_benchmarks

  - name: Linux_pixel_7pro platform_channel_sample_test
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: platform_channel_sample_test

  - name: Linux_pixel_7pro platform_interaction_test
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: platform_interaction_test

  # linux mokey benchmark
  - name: Linux_mokey platform_views_scroll_perf__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: platform_views_scroll_perf__timeline_summary

  - name: Linux_pixel_7pro platform_views_scroll_perf__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: platform_views_scroll_perf__timeline_summary

  # linux mokey benchmark
  - name: Linux_mokey platform_views_scroll_perf_impeller__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      ignore_flakiness: "true"
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: platform_views_scroll_perf_impeller__timeline_summary

  - name: Linux_pixel_7pro platform_views_scroll_perf_impeller__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: platform_views_scroll_perf_impeller__timeline_summary

  - name: Linux_pixel_7pro platform_views_hcpp_scroll_perf__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: platform_views_hcpp_scroll_perf__timeline_summary

  # linux mokey benchmark
  - name: Linux_mokey platform_view__start_up
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: platform_view__start_up

  - name: Linux_pixel_7pro routing_test
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: routing_test

  - name: Linux_pixel_7pro service_extensions_test
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: service_extensions_test

  # linux mokey benchmark
  - name: Linux_mokey textfield_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: textfield_perf__e2e_summary

  - name: Linux_pixel_7pro textfield_perf__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: textfield_perf__timeline_summary

  # linux mokey benchmark
  - name: Linux_mokey tiles_scroll_perf__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab","android","linux", "mokey"]
      task_name: tiles_scroll_perf__timeline_summary
      dependencies: >-
        [
          {"dependency": "open_jdk", "version": "version:21"}
        ]

  - name: Linux web_size__compile_test
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "hostonly", "linux"]
      task_name: web_size__compile_test

  # linux mokey benchmark
  - name: Linux_mokey opacity_peephole_one_rect_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: opacity_peephole_one_rect_perf__e2e_summary

  # linux mokey benchmark
  - name: Linux_mokey opacity_peephole_col_of_rows_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: opacity_peephole_col_of_rows_perf__e2e_summary

  # linux mokey benchmark
  - name: Linux_mokey opacity_peephole_opacity_of_grid_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: opacity_peephole_opacity_of_grid_perf__e2e_summary

  # linux mokey benchmark
  - name: Linux_mokey opacity_peephole_grid_of_opacity_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: opacity_peephole_grid_of_opacity_perf__e2e_summary

  # linux mokey benchmark
  - name: Linux_mokey opacity_peephole_fade_transition_text_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: opacity_peephole_fade_transition_text_perf__e2e_summary

  # linux mokey benchmark
  - name: Linux_mokey opacity_peephole_grid_of_alpha_savelayers_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: opacity_peephole_grid_of_alpha_savelayers_perf__e2e_summary

  # linux mokey benchmark
  - name: Linux_mokey opacity_peephole_col_of_alpha_savelayer_rows_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: opacity_peephole_col_of_alpha_savelayer_rows_perf__e2e_summary

  # linux mokey benchmark
  - name: Linux_mokey gradient_dynamic_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: gradient_dynamic_perf__e2e_summary

  # linux mokey benchmark
  - name: Linux_mokey gradient_consistent_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: gradient_consistent_perf__e2e_summary

  # linux mokey benchmark
  - name: Linux_mokey gradient_static_perf__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: gradient_static_perf__e2e_summary

  - name: Linux_pixel_7pro android_choreographer_do_frame_test
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: android_choreographer_do_frame_test

  # linux mokey benchmark
  - name: Linux_mokey animated_blur_backdrop_filter_perf__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "mokey"]
      task_name: animated_blur_backdrop_filter_perf__timeline_summary

  # Uses Impeller.
  - name: Linux_pixel_7pro animated_blur_backdrop_filter_perf__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: animated_blur_backdrop_filter_perf__timeline_summary

  # Uses Impeller.
  - name: Linux_pixel_7pro animated_advanced_blend_perf_opengles__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: animated_advanced_blend_perf_opengles__timeline_summary

  # Uses Impeller.
  - name: Linux_pixel_7pro animated_advanced_blend_perf__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: animated_advanced_blend_perf__timeline_summary

  - name: Mac_ios animated_advanced_blend_perf_ios__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: animated_advanced_blend_perf_ios__timeline_summary

  # Uses Impeller.
  - name: Linux_pixel_7pro rrect_blur_perf__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: rrect_blur_perf__timeline_summary

  - name: Mac_ios rrect_blur_perf_ios__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: rrect_blur_perf_ios__timeline_summary

  # Uses Impeller.
  - name: Linux_pixel_7pro animated_blur_backdrop_filter_perf_opengles__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: animated_blur_backdrop_filter_perf_opengles__timeline_summary

  # Uses Impeller.
  - name: Linux_pixel_7pro draw_vertices_perf_opengles__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: draw_vertices_perf_opengles__timeline_summary

  # Uses Impeller.
  - name: Linux_pixel_7pro draw_vertices_perf__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "linux", "pixel", "7pro"]
      task_name: draw_vertices_perf__timeline_summary

  - name: Mac_ios draw_vertices_perf_ios__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: draw_vertices_perf_ios__timeline_summary

  - name: Mac_ios draw_atlas_perf_ios__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: draw_atlas_perf_ios__timeline_summary

  - name: Mac_ios static_path_tessellation_perf_ios__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: static_path_tessellation_perf_ios__timeline_summary

  - name: Mac_ios dynamic_path_tessellation_perf_ios__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: dynamic_path_tessellation_perf_ios__timeline_summary

  - name: Staging_build_linux analyze
    presubmit: false
    bringup: true
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      shard: analyze
      ignore_flakiness: "true"
      tags: >
        ["framework","hostonly","shard","linux"]

  - name: Mac_benchmark animated_complex_opacity_perf_macos__e2e_summary
    presubmit: false
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      task_name: animated_complex_opacity_perf_macos__e2e_summary

  - name: Mac_benchmark basic_material_app_macos__compile
    presubmit: false
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      task_name: basic_material_app_macos__compile

  - name: Mac build_ios_framework_module_test
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac"]
      task_name: build_ios_framework_module_test
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac_arm64 build_ios_framework_module_test
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac", "arm64"]
      task_name: build_ios_framework_module_test
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac_x64 build_tests_1_4
    recipe: flutter/flutter_drone
    presubmit: false # Rely on Mac_arm build_tests in presubmit.
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: build_tests
      subshard: "1_4"
      tags: >
        ["framework", "hostonly", "shard", "mac"]

  - name: Mac_x64 build_tests_2_4
    recipe: flutter/flutter_drone
    presubmit: false # Rely on Mac_arm build_tests in presubmit.
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: build_tests
      subshard: "2_4"
      tags: >
        ["framework", "hostonly", "shard", "mac"]

  - name: Mac_x64 build_tests_3_4
    recipe: flutter/flutter_drone
    presubmit: false # Rely on Mac_arm build_tests in presubmit.
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: build_tests
      subshard: "3_4"
      tags: >
        ["framework", "hostonly", "shard", "mac"]

  - name: Mac_x64 build_tests_4_4
    recipe: flutter/flutter_drone
    presubmit: false # Rely on Mac_arm build_tests in presubmit.
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: build_tests
      subshard: "4_4"
      tags: >
        ["framework", "hostonly", "shard", "mac"]

  - name: Mac_arm64 build_tests_1_4
    recipe: flutter/flutter_drone
    timeout: 60
    # Flake rate of >2.5% in presubmit
    presubmit: false # https://github.com/flutter/flutter/issues/150642
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: build_tests
      subshard: "1_4"
      tags: >
        ["framework", "hostonly", "shard", "mac"]

  - name: Mac_arm64 build_tests_2_4
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: build_tests
      subshard: "2_4"
      tags: >
        ["framework", "hostonly", "shard", "mac"]

  - name: Mac_arm64 build_tests_3_4
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: build_tests
      subshard: "3_4"
      tags: >
        ["framework", "hostonly", "shard", "mac"]

  - name: Mac_arm64 build_tests_4_4
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: build_tests
      subshard: "4_4"
      tags: >
        ["framework", "hostonly", "shard", "mac"]

  - name: Mac_benchmark complex_layout_macos__start_up
    presubmit: false
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      task_name: complex_layout_macos__start_up

  - name: Mac_benchmark complex_layout_scroll_perf_macos__timeline_summary
    presubmit: false
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      task_name: complex_layout_scroll_perf_macos__timeline_summary

  - name: Mac customer_testing
    enabled_branches:
      - master
    recipe: flutter/flutter_drone
    # Timeout in minutes for the whole task.
    timeout: 60
    properties:
      shard: customer_testing
      tags: >
        ["framework", "hostonly", "shard", "mac"]
      test_timeout_secs: "2700" # Allows 45 minutes (up from 30 default)

  - name: Mac dart_plugin_registry_test
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac"]
      task_name: dart_plugin_registry_test
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac flavors_test_macos
    presubmit: false
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac"]
      task_name: flavors_test_macos

  - name: Mac_benchmark flutter_gallery_macos__compile
    presubmit: false
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      task_name: flutter_gallery_macos__compile

  - name: Mac flutter_packaging_test
    recipe: packaging/packaging
    presubmit: false
    enabled_branches:
      - master
    properties:
      task_name: flutter_packaging
      tags: >
        ["framework", "hostonly", "shard", "mac"]
    runIf:
      - .ci.yaml
      - engine/**
      - DEPS
      - dev/bots/**

  - name: Mac_arm64 flutter_packaging_test
    recipe: packaging/packaging
    presubmit: false
    enabled_branches:
      - master
    properties:
      task_name: flutter_packaging
      tags: >
        ["framework", "hostonly", "shard", "mac"]
    runIf:
      - .ci.yaml
      - engine/**
      - DEPS
      - dev/bots/**

  - name: Mac_benchmark flutter_view_macos__start_up
    presubmit: false
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      task_name: flutter_view_macos__start_up

  - name: Mac framework_tests_libraries
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      cpu: x86 # https://github.com/flutter/flutter/issues/119880
      dependencies: >-
        [
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: framework_tests
      subshard: libraries
      tags: >
        ["framework", "hostonly", "shard", "mac"]
    runIf:
      - dev/**
      - packages/flutter/**
      - packages/flutter_driver/**
      - packages/integration_test/**
      - packages/flutter_localizations/**
      - packages/fuchsia_remote_debug_protocol/**
      - packages/flutter_test/**
      - packages/flutter_goldens/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac framework_tests_impeller
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      cpu: x86 # https://github.com/flutter/flutter/issues/119880
      dependencies: >-
        [
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: framework_tests
      subshard: impeller
      tags: >
        ["framework", "hostonly", "shard", "mac"]
    runIf:
      - dev/**
      - packages/flutter/**
      - packages/flutter_driver/**
      - packages/integration_test/**
      - packages/flutter_localizations/**
      - packages/fuchsia_remote_debug_protocol/**
      - packages/flutter_test/**
      - packages/flutter_goldens/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac_x64 framework_tests_misc
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "android_sdk", "version": "version:35v1"}
        ]
      shard: framework_tests
      subshard: misc
      tags: >
        ["framework", "hostonly", "shard", "mac"]
    runIf:
      - dev/**
      - examples/api/**
      - packages/flutter/**
      - packages/flutter_driver/**
      - packages/integration_test/**
      - packages/flutter_localizations/**
      - packages/fuchsia_remote_debug_protocol/**
      - packages/flutter_test/**
      - packages/flutter_goldens/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac_arm64 framework_tests_misc
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "android_sdk", "version": "version:35v1"}
        ]
      shard: framework_tests
      subshard: misc
      tags: >
        ["framework", "hostonly", "shard", "mac"]
    runIf:
      - dev/**
      - examples/api/**
      - packages/flutter/**
      - packages/flutter_driver/**
      - packages/integration_test/**
      - packages/flutter_localizations/**
      - packages/fuchsia_remote_debug_protocol/**
      - packages/flutter_test/**
      - packages/flutter_goldens/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac framework_tests_widgets
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      cpu: x86 # https://github.com/flutter/flutter/issues/119880
      dependencies: >-
        [
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: framework_tests
      subshard: widgets
      tags: >
        ["framework", "hostonly", "shard", "mac"]
    runIf:
      - dev/**
      - packages/flutter/**
      - packages/flutter_driver/**
      - packages/integration_test/**
      - packages/flutter_localizations/**
      - packages/fuchsia_remote_debug_protocol/**
      - packages/flutter_test/**
      - packages/flutter_goldens/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac gradle_plugin_bundle_test
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac"]
      task_name: gradle_plugin_bundle_test
    runIf:
      - dev/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac_benchmark hello_world_macos__compile
    presubmit: false
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      task_name: hello_world_macos__compile

  - name: Mac integration_ui_test_test_macos
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      tags: >
        ["devicelab", "mac"]
      task_name: integration_ui_test_test_macos

  - name: Mac module_custom_host_app_name_test
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac"]
      task_name: module_custom_host_app_name_test
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac module_host_with_custom_build_test
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac"]
      task_name: module_host_with_custom_build_test
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac build_android_host_app_with_module_aar
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac"]
      task_name: build_android_host_app_with_module_aar
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac build_android_host_app_with_module_source
    recipe: devicelab/devicelab_drone
    timeout: 60
    presubmit: false
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac"]
      task_name: build_android_host_app_with_module_source
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac_arm64 module_test_ios
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac"]
      task_name: module_test_ios
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac_benchmark platform_view_macos__start_up
    presubmit: false
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      task_name: platform_view_macos__start_up

  - name: Mac platform_channel_sample_test_macos
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "hostonly", "mac"]
      task_name: platform_channel_sample_test_macos

  - name: Mac plugin_dependencies_test
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac"]
      task_name: plugin_dependencies_test
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac_x64 plugin_lint_mac
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac"]
      task_name: plugin_lint_mac
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - packages/integration_test/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac_arm64 plugin_lint_mac
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac", "arm64"]
      task_name: plugin_lint_mac
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - packages/integration_test/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac plugin_test
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac"]
      task_name: plugin_test
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac plugin_test_ios
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac"]
      task_name: plugin_test_ios
      # Retry for flakes caused by https://github.com/flutter/flutter/issues/151772
      presubmit_max_attempts: "2"
      test_timeout_secs: "3600"
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac plugin_test_macos
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac"]
      task_name: plugin_test_macos
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac_x64 tool_host_cross_arch_tests
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      shard: tool_host_cross_arch_tests
      tags: >
        ["framework", "hostonly", "shard", "mac"]
      test_timeout_secs: "2700"
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac_arm64 tool_host_cross_arch_tests
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      shard: tool_host_cross_arch_tests
      tags: >
        ["framework", "hostonly", "shard", "mac"]
      test_timeout_secs: "2700"
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac tool_integration_tests_1_5
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      cpu: arm64
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: tool_integration_tests
      subshard: "1_5"
      tags: >
        ["framework", "hostonly", "shard", "mac"]
      test_timeout_secs: "2700"
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac tool_integration_tests_2_5
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      cpu: arm64
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: tool_integration_tests
      subshard: "2_5"
      tags: >
        ["framework", "hostonly", "shard", "mac"]
      test_timeout_secs: "2700"
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac tool_integration_tests_3_5
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      cpu: arm64
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: tool_integration_tests
      subshard: "3_5"
      tags: >
        ["framework", "hostonly", "shard", "mac"]
      test_timeout_secs: "2700"
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac tool_integration_tests_4_5
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      cpu: arm64
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: tool_integration_tests
      subshard: "4_5"
      tags: >
        ["framework", "hostonly", "shard", "mac"]
      test_timeout_secs: "2700"
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac tool_integration_tests_5_5
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      cpu: arm64
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: tool_integration_tests
      subshard: "5_5"
      tags: >
        ["framework", "hostonly", "shard", "mac"]
      test_timeout_secs: "2700"
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac_x64 tool_tests_commands
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      shard: tool_tests
      subshard: commands
      tags: >
        ["framework", "hostonly", "shard", "mac"]

  - name: Mac_arm64 tool_tests_commands
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      shard: tool_tests
      subshard: commands
      tags: >
        ["framework", "hostonly", "shard", "mac"]

  - name: Mac tool_tests_general
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      shard: tool_tests
      subshard: general
      tags: >
        ["framework", "hostonly", "shard", "mac"]
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac_x64 verify_binaries_codesigned
    enabled_branches:
      - flutter-\d+\.\d+-candidate\.\d+
    recipe: flutter/flutter_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["framework", "hostonly", "shard", "mac"]
      shard: verify_binaries_codesigned

  - name: Mac_arm64 verify_binaries_codesigned
    enabled_branches:
      - flutter-\d+\.\d+-candidate\.\d+
    recipe: flutter/flutter_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["framework", "hostonly", "shard", "mac"]
      shard: verify_binaries_codesigned

  - name: Mac web_tool_tests
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: web_tool_tests
      subshard: "1_1"
      tags: >
        ["framework", "hostonly", "shard", "mac"]
      test_timeout_secs: "2700" # Allows 45 minutes (up from 30 default)
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  # mac mokey test
  - name: Mac_arm64_mokey entrypoint_dart_registrant
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "mac", "arm64", "mokey"]
      task_name: entrypoint_dart_registrant
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  # mac mokey benchmark
  - name: Mac_mokey hello_world_android__compile
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "mac", "mokey"]
      task_name: hello_world_android__compile

  # mac mokey test
  - name: Mac_arm64_mokey hello_world_android__compile
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "mac", "arm64", "mokey"]
      task_name: hello_world_android__compile

  # mac mokey benchmark
  - name: Mac_mokey hot_mode_dev_cycle__benchmark
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "mac", "mokey"]
      task_name: hot_mode_dev_cycle__benchmark

  # mac mokey test
  - name: Mac_mokey integration_test_test
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "mac", "mokey"]
      task_name: integration_test_test

  # mac mokey test
  - name: Mac_arm64_mokey integration_test_test
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "mac", "arm64", "mokey"]
      task_name: integration_test_test

  # mac mokey test
  - name: Mac_arm64_mokey integration_ui_frame_number
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "mac", "arm64", "mokey"]
      task_name: integration_ui_frame_number

  # mac mokey benchmark
  - name: Mac_mokey microbenchmarks
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "mac", "mokey"]
      task_name: microbenchmarks

  # mac mokey test
  - name: Mac_arm64_mokey native_assets_android
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "mac", "arm64", "mokey"]
      task_name: native_assets_android

  # mac mokey test
  - name: Mac_mokey run_debug_test_android
    bringup: true # Flaky https://github.com/flutter/flutter/issues/161655
    recipe: devicelab/devicelab_drone
    presubmit: false
    runIf:
      - .ci.yaml
      - dev/**
      - DEPS
      - engine/**
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "mac", "mokey"]
      task_name: run_debug_test_android

  # mac mokey test
  - name: Mac_arm64_mokey run_debug_test_android
    recipe: devicelab/devicelab_drone
    presubmit: false
    runIf:
      - .ci.yaml
      - engine/**
      - DEPS
      - dev/**
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "mac", "arm64", "mokey"]
      task_name: run_debug_test_android

  # mac mokey test
  - name: Mac_mokey run_release_test
    bringup: true # Flaky https://github.com/flutter/flutter/issues/153830
    recipe: devicelab/devicelab_drone
    presubmit: false
    runIf:
      - .ci.yaml
      - engine/**
      - DEPS
      - dev/**
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "mac", "mokey"]
      task_name: run_release_test

  # mac mokey test
  - name: Mac_arm64_mokey run_release_test
    recipe: devicelab/devicelab_drone
    presubmit: false
    runIf:
      - .ci.yaml
      - engine/**
      - DEPS
      - dev/**
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "mac", "arm64", "mokey"]
      task_name: run_release_test

  - name: Mac_ios animation_with_microtasks_perf_ios__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: animation_with_microtasks_perf_ios__timeline_summary

  - name: Mac_ios backdrop_filter_perf_ios__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: backdrop_filter_perf_ios__timeline_summary

  - name: Mac_arm64_ios basic_material_app_ios__compile
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: basic_material_app_ios__compile

  - name: Mac_ios channels_integration_test_ios
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: channels_integration_test_ios

  - name: Mac_ios complex_layout_ios__start_up
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: complex_layout_ios__start_up

  - name: Mac_ios complex_layout_scroll_perf_ios__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: complex_layout_scroll_perf_ios__timeline_summary

  - name: Mac_ios complex_layout_scroll_perf_bad_ios__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: complex_layout_scroll_perf_bad_ios__timeline_summary

  - name: Mac_ios color_filter_and_fade_perf_ios__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: color_filter_and_fade_perf_ios__e2e_summary

  - name: Mac_ios imagefiltered_transform_animation_perf_ios__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: imagefiltered_transform_animation_perf_ios__timeline_summary

  # TODO(https://github.com/flutter/flutter/issues/106806): Find a way to
  # re-enable this without "ignore_flakiness: "true"", likely by loostening the
  # test assertions, or potentially not running the frame rate tests at all on
  # iOS (for example, doing pixel-tests instead).
  #
  # Also, rename this to "external_textures_integration_test" to be consistent
  # with the Android test, but that can wait until we've figured out the flake.
  - name: Mac_ios external_ui_integration_test_ios
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: external_textures_integration_test_ios
      ignore_flakiness: "true"

  - name: Mac_ios route_test_ios
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: route_test_ios

  - name: Mac_ios flavors_test_ios
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: flavors_test_ios

  - name: Mac_arm64_ios flutter_gallery_ios__compile
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac", "arm64"]
      task_name: flutter_gallery_ios__compile

  - name: Mac_ios flutter_gallery_ios__start_up
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: flutter_gallery_ios__start_up

  - name: Mac_ios flutter_view_ios__start_up
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: flutter_view_ios__start_up

  - name: Mac_arm64_ios hello_world_ios__compile
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac", "arm64"]
      task_name: hello_world_ios__compile

  - name: Mac_arm64_ios imitation_game_flutter
    recipe: devicelab/devicelab_drone
    presubmit: false
    bringup: true
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac", "arm64"]
      task_name: imitation_game_swiftui__compile

  - name: Mac_arm64_ios imitation_game_swiftui
    recipe: devicelab/devicelab_drone
    presubmit: false
    bringup: true
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac", "arm64"]
      task_name: imitation_game_flutter__compile

  - name: Mac_x64 hot_mode_dev_cycle_macos_target__benchmark
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac"]
      task_name: hot_mode_dev_cycle_macos_target__benchmark
    runIf:
      - dev/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac_arm64 hot_mode_dev_cycle_macos_target__benchmark
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac", "arm64"]
      task_name: hot_mode_dev_cycle_macos_target__benchmark
    runIf:
      - .ci.yaml
      - engine/**
      - DEPS
      - dev/**

  - name: Mac_x64_ios integration_test_test_ios
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: integration_test_test_ios

  - name: Mac_arm64_ios integration_test_test_ios
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: integration_test_test_ios

  - name: Mac_ios integration_ui_ios_driver
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: integration_ui_ios_driver

  - name: Mac_ios integration_ui_ios_frame_number
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: integration_ui_ios_frame_number

  - name: Mac_ios integration_ui_ios_keyboard_resize
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: integration_ui_ios_keyboard_resize

  - name: Mac_ios integration_ui_ios_textfield
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: integration_ui_ios_textfield

  - name: Mac_x64 ios_app_with_extensions_test
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac"]
      task_name: ios_app_with_extensions_test

  - name: Mac_arm64 ios_app_with_extensions_test
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac", "arm64"]
      task_name: ios_app_with_extensions_test

  - name: Mac_ios ios_defines_test
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: ios_defines_test

  - name: Mac_ios ios_platform_view_tests
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: ios_platform_view_tests

  - name: Mac_ios large_image_changer_perf_ios
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: large_image_changer_perf_ios

  - name: Mac_x64 macos_chrome_dev_mode
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac"]
      task_name: macos_chrome_dev_mode

  - name: Mac_arm64 macos_chrome_dev_mode
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac", "arm64"]
      task_name: macos_chrome_dev_mode

  - name: Mac_ios microbenchmarks_ios
    recipe: devicelab/devicelab_drone
    presubmit: false
    bringup: true
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: microbenchmarks_ios

  - name: Mac native_assets_ios_simulator
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac"]
      task_name: native_assets_ios_simulator

  - name: Mac_ios native_assets_ios
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: native_assets_ios

  - name: Mac_ios native_platform_view_ui_tests_ios
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: native_platform_view_ui_tests_ios

  - name: Mac_ios new_gallery_ios__transition_perf
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: new_gallery_ios__transition_perf

  - name: Mac_ios new_gallery_skia_ios__transition_perf
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: new_gallery_skia_ios__transition_perf

  - name: Mac_ios platform_channel_sample_test_ios
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: platform_channel_sample_test_ios

  - name: Mac_ios platform_channel_sample_test_swift
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: platform_channel_sample_test_swift

  - name: Mac_ios platform_channels_benchmarks_ios
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: platform_channels_benchmarks_ios

  - name: Mac_ios platform_interaction_test_ios
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: platform_interaction_test_ios

  - name: Mac_ios platform_view_ios__start_up
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: platform_view_ios__start_up

  - name: Mac_ios platform_views_scroll_perf_ios__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: platform_views_scroll_perf_ios__timeline_summary

  - name: Mac_ios platform_views_scroll_perf_ad_banners__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: platform_views_scroll_perf_ad_banners__timeline_summary

  - name: Mac_ios platform_views_scroll_perf_bottom_ad_banner__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: platform_views_scroll_perf_bottom_ad_banner__timeline_summary

  - name: Mac_ios platform_views_scroll_perf_non_intersecting_impeller_ios__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: platform_views_scroll_perf_non_intersecting_impeller_ios__timeline_summary

  - name: Mac_ios post_backdrop_filter_perf_ios__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: post_backdrop_filter_perf_ios__timeline_summary

  - name: Mac_ios simple_animation_perf_ios
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: simple_animation_perf_ios

  - name: Mac_ios wide_gamut_ios
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: wide_gamut_ios

  - name: Mac_x64_ios hot_mode_dev_cycle_ios__benchmark
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: hot_mode_dev_cycle_ios__benchmark

  - name: Mac_arm64_ios hot_mode_dev_cycle_ios_beta__benchmark
    recipe: devicelab/devicelab_drone
    presubmit: false
    bringup: true
    timeout: 60
    properties:
      os: Mac-15
      device_os: iOS-18.4
      $flutter/osx_sdk : >-
        {
          "sdk_version": "16e5104o"
        }
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: hot_mode_dev_cycle_ios__benchmark

  - name: Mac_arm64_ios hot_mode_dev_cycle_ios__benchmark
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      # TODO(vashworth): Use "hot_mode_dev_cycle_ios__benchmark" once https://github.com/flutter/flutter/issues/142305 is fixed.
      task_name: hot_mode_dev_cycle_ios__benchmark_no_dds

  - name: Mac_x64 hot_mode_dev_cycle_ios_simulator
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac"]
      task_name: hot_mode_dev_cycle_ios_simulator

  - name: Mac_ios fullscreen_textfield_perf_ios__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: fullscreen_textfield_perf_ios__e2e_summary

  - name: Mac_ios very_long_picture_scrolling_perf_ios__e2e_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 120
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: very_long_picture_scrolling_perf_ios__e2e_summary

  - name: Mac_ios tiles_scroll_perf_ios__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: tiles_scroll_perf_ios__timeline_summary

  - name: Mac_build_test flutter_gallery__transition_perf_e2e_ios
    recipe: devicelab/devicelab_drone_build_test
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: flutter_gallery__transition_perf_e2e_ios
      drone_dimensions: >
        ["device_os=iOS-17|iOS-18","os=Mac-14", "cpu=x86"]

  - name: Mac_ios animated_blur_backdrop_filter_perf_ios__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: animated_blur_backdrop_filter_perf_ios__timeline_summary

  - name: Mac_ios draw_points_perf_ios__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: draw_points_perf_ios__timeline_summary

  - name: Mac_ios spell_check_test
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "ios", "mac"]
      task_name: spell_check_test_ios

  - name: Mac_x64 native_ui_tests_macos
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac"]
      task_name: native_ui_tests_macos
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac_arm64 native_ui_tests_macos
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac"]
      task_name: native_ui_tests_macos
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac channels_integration_test
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac"]
      task_name: channels_integration_test_macos
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac run_debug_test_macos
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac"]
      task_name: run_debug_test_macos
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac_arm64 run_debug_test_macos
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac", "arm64"]
      task_name: run_debug_test_macos
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac_arm64 run_release_test_macos
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac", "arm64"]
      task_name: run_release_test_macos
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Mac_arm64 mac_desktop_impeller
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "ruby", "version": "ruby_3.1-pod_1.13"}
        ]
      tags: >
        ["devicelab", "hostonly", "mac", "arm64"]
      task_name: run_release_test_macos

  - name: Windows build_tests_1_9
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      shard: build_tests
      subshard: "1_9"
      tags: >
        ["framework", "hostonly", "shard", "windows"]

  - name: Windows build_tests_2_9
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      shard: build_tests
      subshard: "2_9"
      tags: >
        ["framework", "hostonly", "shard", "windows"]

  - name: Windows build_tests_3_9
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      shard: build_tests
      subshard: "3_9"
      tags: >
        ["framework", "hostonly", "shard", "windows"]

  - name: Windows build_tests_4_9
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      shard: build_tests
      subshard: "4_9"
      tags: >
        ["framework", "hostonly", "shard", "windows"]

  - name: Windows build_tests_5_9
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      shard: build_tests
      subshard: "5_9"
      tags: >
        ["framework", "hostonly", "shard", "windows"]

  - name: Windows build_tests_6_9
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      shard: build_tests
      subshard: "6_9"
      tags: >
        ["framework", "hostonly", "shard", "windows"]

  - name: Windows build_tests_7_9
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      shard: build_tests
      subshard: "7_9"
      tags: >
        ["framework", "hostonly", "shard", "windows"]

  - name: Windows build_tests_8_9
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      shard: build_tests
      subshard: "8_9"
      tags: >
        ["framework", "hostonly", "shard", "windows"]

  - name: Windows build_tests_9_9
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      shard: build_tests
      subshard: "9_9"
      tags: >
        ["framework", "hostonly", "shard", "windows"]

  - name: Windows customer_testing
    enabled_branches:
      - master
    recipe: flutter/flutter_drone
    # Timeout in minutes for the whole task.
    timeout: 60
    properties:
      shard: customer_testing
      tags: >
        ["framework", "hostonly", "shard", "windows"]
      test_timeout_secs: "2700" # Allows 45 minutes (up from 30 default)

  - name: Windows framework_tests_libraries
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: framework_tests
      subshard: libraries
      tags: >
        ["framework", "hostonly", "shard", "windows"]
    runIf:
      - dev/**
      - packages/flutter/**
      - packages/flutter_driver/**
      - packages/integration_test/**
      - packages/flutter_localizations/**
      - packages/fuchsia_remote_debug_protocol/**
      - packages/flutter_test/**
      - packages/flutter_goldens/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows framework_tests_libraries_leak_tracking
    recipe: flutter/flutter_drone
    timeout: 120
    properties:
      test_timeout_secs: "3600" # 1 hour
      dependencies: >-
        [
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: framework_tests
      subshard: libraries
      tags: >
        ["framework", "hostonly", "shard", "windows"]
      env_variables: >-
        {
          "LEAK_TRACKING": "true",
          "TEST_RANDOMIZATION_OFF": "true"
        }
    runIf:
      - dev/**
      - packages/flutter/**
      - packages/flutter_driver/**
      - packages/integration_test/**
      - packages/flutter_localizations/**
      - packages/fuchsia_remote_debug_protocol/**
      - packages/flutter_test/**
      - packages/flutter_goldens/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows framework_tests_misc
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "vs_build", "version": "version:vs2019"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "android_sdk", "version": "version:35v1"}
        ]
      shard: framework_tests
      subshard: misc
      tags: >
        ["framework", "hostonly", "shard", "windows"]
    runIf:
      - dev/**
      - examples/api/**
      - packages/flutter/**
      - packages/flutter_driver/**
      - packages/integration_test/**
      - packages/flutter_localizations/**
      - packages/fuchsia_remote_debug_protocol/**
      - packages/flutter_test/**
      - packages/flutter_goldens/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows framework_tests_widgets
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: framework_tests
      subshard: widgets
      tags: >
        ["framework", "hostonly", "shard", "windows"]
    runIf:
      - dev/**
      - packages/flutter/**
      - packages/flutter_driver/**
      - packages/integration_test/**
      - packages/flutter_localizations/**
      - packages/fuchsia_remote_debug_protocol/**
      - packages/flutter_test/**
      - packages/flutter_goldens/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows framework_tests_widgets_leak_tracking
    recipe: flutter/flutter_drone
    timeout: 120
    properties:
      test_timeout_secs: "3600" # 1 hour
      dependencies: >-
        [
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: framework_tests
      subshard: widgets
      tags: >
        ["framework", "hostonly", "shard", "windows"]
      env_variables: >-
        {
          "LEAK_TRACKING": "true",
          "TEST_RANDOMIZATION_OFF": "true"
        }
    runIf:
      - dev/**
      - packages/flutter/**
      - packages/flutter_driver/**
      - packages/integration_test/**
      - packages/flutter_localizations/**
      - packages/fuchsia_remote_debug_protocol/**
      - packages/flutter_test/**
      - packages/flutter_goldens/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows gradle_plugin_bundle_test
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      tags: >
        ["devicelab", "hostonly", "windows"]
      task_name: gradle_plugin_bundle_test
    runIf:
      - dev/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows hot_mode_dev_cycle_win_target__benchmark
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      tags: >
        ["devicelab", "hostonly", "windows"]
      task_name: hot_mode_dev_cycle_win_target__benchmark

  - name: Windows_arm64 hot_mode_dev_cycle_win_target__benchmark
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      tags: >
        ["devicelab", "hostonly", "windows", "arm64"]
      task_name: hot_mode_dev_cycle_win_target__benchmark

  - name: Windows module_custom_host_app_name_test
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      tags: >
        ["devicelab", "hostonly", "windows"]
      task_name: module_custom_host_app_name_test
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows module_host_with_custom_build_test
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      tags: >
        ["devicelab", "hostonly", "windows"]
      task_name: module_host_with_custom_build_test
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows build_android_host_app_with_module_aar
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      tags: >
        ["devicelab", "hostonly", "windows"]
      task_name: build_android_host_app_with_module_aar
      test_timeout_secs: "2700" # Allows 45 minutes (up from 30 default)
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows build_android_host_app_with_module_source
    recipe: devicelab/devicelab_drone
    timeout: 60
    presubmit: false
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      tags: >
        ["devicelab", "hostonly", "windows"]
      task_name: build_android_host_app_with_module_source
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows platform_channel_sample_test_windows
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      tags: >
        ["devicelab", "hostonly", "windows"]
      task_name: platform_channel_sample_test_windows

  - name: Windows_arm64 platform_channel_sample_test_windows
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      tags: >
        ["devicelab", "hostonly", "windows", "arm64"]
      task_name: platform_channel_sample_test_windows

  - name: Windows plugin_dependencies_test
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      tags: >
        ["devicelab", "hostonly", "windows"]
      task_name: plugin_dependencies_test
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows plugin_test
    bringup: true # Flaky https://github.com/flutter/flutter/issues/148834
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      tags: >
        ["devicelab", "hostonly", "windows"]
      task_name: plugin_test
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows plugin_test_windows
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      tags: >
        ["devicelab", "hostonly", "windows"]
      task_name: plugin_test_windows
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows_arm64 plugin_test_windows
    bringup: true # Flaky https://github.com/flutter/flutter/issues/163122
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      tags: >
        ["devicelab", "hostonly", "windows", "arm64"]
      task_name: plugin_test_windows
      test_timeout_secs: "900" # 15 minutes
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows run_debug_test_windows
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      tags: >
        ["devicelab", "hostonly", "windows"]
      task_name: run_debug_test_windows
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows_arm64 run_debug_test_windows
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      tags: >
        ["devicelab", "hostonly", "windows", "arm64"]
      task_name: run_debug_test_windows
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows run_release_test_windows
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      tags: >
        ["devicelab", "hostonly", "windows"]
      task_name: run_release_test_windows
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows_arm64 run_release_test_windows
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      tags: >
        ["devicelab", "hostonly", "windows", "arm64"]
      task_name: run_release_test_windows
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows tool_integration_tests_1_9
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      shard: tool_integration_tests
      subshard: "1_9"
      tags: >
        ["framework", "hostonly", "shard", "windows"]
      test_timeout_secs: "2700"
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows tool_integration_tests_2_9
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      shard: tool_integration_tests
      subshard: "2_9"
      tags: >
        ["framework", "hostonly", "shard", "windows"]
      test_timeout_secs: "2700"
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows tool_integration_tests_3_9
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      shard: tool_integration_tests
      subshard: "3_9"
      tags: >
        ["framework", "hostonly", "shard", "windows"]
      test_timeout_secs: "2700"
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows tool_integration_tests_4_9
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      shard: tool_integration_tests
      subshard: "4_9"
      tags: >
        ["framework", "hostonly", "shard", "windows"]
      test_timeout_secs: "2700"
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows tool_integration_tests_5_9
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      shard: tool_integration_tests
      subshard: "5_9"
      tags: >
        ["framework", "hostonly", "shard", "windows"]
      test_timeout_secs: "2700"
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows tool_integration_tests_6_9
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      shard: tool_integration_tests
      subshard: "6_9"
      tags: >
        ["framework", "hostonly", "shard", "windows"]
      test_timeout_secs: "2700"
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows tool_integration_tests_7_9
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      shard: tool_integration_tests
      subshard: "7_9"
      tags: >
        ["framework", "hostonly", "shard", "windows"]
      test_timeout_secs: "2700"
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows tool_integration_tests_8_9
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      shard: tool_integration_tests
      subshard: "8_9"
      tags: >
        ["framework", "hostonly", "shard", "windows"]
      test_timeout_secs: "2700"
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows tool_integration_tests_9_9
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"},
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      shard: tool_integration_tests
      subshard: "9_9"
      tags: >
        ["framework", "hostonly", "shard", "windows"]
      test_timeout_secs: "2700"
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows tool_tests_commands
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      shard: tool_tests
      subshard: commands
      tags: >
        ["framework", "hostonly", "shard", "windows"]
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows tool_tests_general
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      add_recipes_cq: "true"
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "open_jdk", "version": "version:21"}
        ]
      shard: tool_tests
      subshard: general
      tags: >
        ["framework", "hostonly", "shard", "windows"]
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows web_tool_tests_1_2
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: web_tool_tests
      subshard: "1_2"
      tags: >
        ["framework", "hostonly", "shard", "windows"]
      test_timeout_secs: "2700"
    runIf:
      - dev/**
      - packages/flutter_tools/**
      - bin/**
      - .ci.yaml
      - engine/**
      - DEPS

  - name: Windows web_tool_tests_2_2
    recipe: flutter/flutter_drone
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "android_sdk", "version": "version:35v1"},
          {"dependency": "chrome_and_driver", "version": "version:125.0.6422.141"},
          {"dependency": "open_jdk", "version": "version:21"},
          {"dependency": "goldctl", "version": "git_revision:2387d6fff449587eecbb7e45b2692ca0710b63b9"}
        ]
      shard: web_tool_tests
      subshard: "2_2"
      tags: >
        ["framework", "hostonly", "shard"]
    runIf:
    - dev/**
    - packages/flutter_tools/**
    - bin/**
    - .ci.yaml
    - engine/**
    - DEPS

  - name: Windows windows_home_scroll_perf__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "hostonly", "windows"]
      dependencies: >-
        [
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      task_name: windows_home_scroll_perf__timeline_summary

  - name: Windows_arm64 windows_home_scroll_perf__timeline_summary
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "hostonly", "windows", "arm64"]
      dependencies: >-
        [
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      task_name: windows_home_scroll_perf__timeline_summary


  - name: Windows hello_world_win_desktop__compile
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "hostonly", "windows"]
      dependencies: >-
        [
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      task_name: hello_world_win_desktop__compile

  - name: Windows windows_desktop_impeller
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "hostonly", "windows"]
      dependencies: >-
        [
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      task_name: windows_desktop_impeller

  - name: Windows_arm64 hello_world_win_desktop__compile
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "hostonly", "windows", "arm64"]
      dependencies: >-
        [
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      task_name: hello_world_win_desktop__compile

  - name: Windows flutter_gallery_win_desktop__compile
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "hostonly", "windows"]
      dependencies: >-
        [
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      task_name: flutter_gallery_win_desktop__compile

  - name: Windows_arm64 flutter_gallery_win_desktop__compile
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "hostonly", "windows", "arm64"]
      dependencies: >-
        [
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      task_name: flutter_gallery_win_desktop__compile

  - name: Windows flutter_gallery_win_desktop__start_up
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "hostonly", "windows"]
      dependencies: >-
        [
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      task_name: flutter_gallery_win_desktop__start_up

  - name: Windows_arm64 flutter_gallery_win_desktop__start_up
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "hostonly", "windows", "arm64"]
      dependencies: >-
        [
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      task_name: flutter_gallery_win_desktop__start_up

  - name: Windows complex_layout_win_desktop__start_up
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "hostonly", "windows"]
      dependencies: >-
        [
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      task_name: complex_layout_win_desktop__start_up

  - name: Windows_arm64 complex_layout_win_desktop__start_up
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "hostonly", "windows", "arm64"]
      dependencies: >-
        [
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      task_name: complex_layout_win_desktop__start_up

  - name: Windows flutter_view_win_desktop__start_up
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "hostonly", "windows"]
      dependencies: >-
        [
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      task_name: flutter_view_win_desktop__start_up

  - name: Windows_arm64 flutter_view_win_desktop__start_up
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "hostonly", "windows", "arm64"]
      dependencies: >-
        [
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      task_name: flutter_view_win_desktop__start_up

  - name: Windows platform_view_win_desktop__start_up
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "hostonly", "windows"]
      dependencies: >-
        [
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      task_name: platform_view_win_desktop__start_up

  - name: Windows_arm64 platform_view_win_desktop__start_up
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "hostonly", "windows", "arm64"]
      dependencies: >-
        [
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      task_name: platform_view_win_desktop__start_up

  # windows mokey test
  - name: Windows_mokey basic_material_app_win__compile
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "windows", "mokey"]
      task_name: basic_material_app_win__compile

  # windows mokey test
  - name: Windows_mokey channels_integration_test_win
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "windows", "mokey"]
      task_name: channels_integration_test_win

  # windows mokey test
  - name: Windows_mokey flavors_test_win
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "windows", "mokey"]
      task_name: flavors_test

  # windows mokey test
  - name: Windows_mokey flutter_gallery_win__compile
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "windows", "mokey"]
      task_name: flutter_gallery_win__compile

  # windows mokey benchmark
  - name: Windows_mokey hot_mode_dev_cycle_win__benchmark
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "windows", "mokey"]
      task_name: hot_mode_dev_cycle_win__benchmark

  # windows mokey test
  - name: Windows_mokey native_assets_android
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "windows", "mokey"]
      task_name: native_assets_android

  # windows mokey test
  - name: Windows_mokey windows_chrome_dev_mode
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "android", "windows", "mokey"]
      task_name: windows_chrome_dev_mode

  - name: Windows flutter_packaging_test
    recipe: packaging/packaging
    presubmit: false
    enabled_branches:
      - master
    properties:
      task_name: flutter_packaging
      tags: >
        ["framework", "hostonly", "shard", "windows"]
    runIf:
      - .ci.yaml
      - engine/**
      - DEPS
      - dev/bots/**

  - name: Windows windows_startup_test
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      tags: >
        ["devicelab", "hostonly", "windows"]
      task_name: windows_startup_test

  - name: Windows_arm64 windows_startup_test
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      dependencies: >-
        [
          {"dependency": "vs_build", "version": "version:vs2019"}
        ]
      tags: >
        ["devicelab", "hostonly", "windows", "arm64"]
      task_name: windows_startup_test

  - name: Windows flutter_tool_startup__windows
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "hostonly", "windows"]
      task_name: flutter_tool_startup

  - name: Windows_arm64 flutter_tool_startup__windows
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "hostonly", "windows", "arm64"]
      task_name: flutter_tool_startup

  - name: Linux flutter_tool_startup__linux
    recipe: devicelab/devicelab_drone
    presubmit: false
    timeout: 60
    properties:
      tags: >
        ["devicelab", "hostonly", "linux"]
      task_name: flutter_tool_startup

  - name: Mac_benchmark flutter_tool_startup__macos
    presubmit: false
    recipe: devicelab/devicelab_drone
    timeout: 60
    properties:
      task_name: flutter_tool_startup

  - name: Linux flutter_packaging
    recipe: packaging/packaging
    timeout: 60
    scheduler: release
    bringup: true # https://github.com/flutter/flutter/issues/126286
    enabled_branches:
      - beta
      - stable
    properties:
      task_name: flutter_packaging
      tags: >
        ["framework", "hostonly", "shard", "linux"]
    drone_dimensions:
      - os=Linux

  - name: Mac flutter_packaging
    recipe: packaging/packaging
    timeout: 60
    scheduler: release
    enabled_branches:
      - beta
      - stable
    properties:
      task_name: flutter_packaging
      tags: >
        ["framework", "hostonly", "shard", "mac"]
    drone_dimensions:
      - os=Mac
      - cpu=x86


  - name: Mac_arm64 flutter_packaging
    recipe: packaging/packaging
    timeout: 60
    scheduler: release
    enabled_branches:
      - beta
      - stable
    properties:
      task_name: flutter_packaging
      tags: >
        ["framework", "hostonly", "shard", "mac"]
    drone_dimensions:
      - os=Mac
      - cpu=arm64

  - name: Windows flutter_packaging
    recipe: packaging/packaging
    timeout: 60
    scheduler: release
    bringup: true
    enabled_branches:
      - beta
      - stable
    properties:
      task_name: flutter_packaging
      tags: >
        ["framework", "hostonly", "shard", "windows"]
    drone_dimensions:
      - os=Windows
