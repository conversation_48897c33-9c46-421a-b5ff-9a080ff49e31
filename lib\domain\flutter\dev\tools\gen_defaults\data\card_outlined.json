{"version": "6_1_0", "md.comp.outlined-card.container.color": "surface", "md.comp.outlined-card.container.elevation": "md.sys.elevation.level0", "md.comp.outlined-card.container.shadow-color": "shadow", "md.comp.outlined-card.container.shape": "md.sys.shape.corner.medium", "md.comp.outlined-card.disabled.container.elevation": "md.sys.elevation.level0", "md.comp.outlined-card.disabled.outline.color": "outline", "md.comp.outlined-card.disabled.outline.opacity": 0.12, "md.comp.outlined-card.dragged.container.elevation": "md.sys.elevation.level3", "md.comp.outlined-card.dragged.outline.color": "outlineVariant", "md.comp.outlined-card.dragged.state-layer.color": "onSurface", "md.comp.outlined-card.dragged.state-layer.opacity": "md.sys.state.dragged.state-layer-opacity", "md.comp.outlined-card.focus.container.elevation": "md.sys.elevation.level0", "md.comp.outlined-card.focus.indicator.color": "secondary", "md.comp.outlined-card.focus.indicator.outline.offset": "md.sys.state.focus-indicator.outer-offset", "md.comp.outlined-card.focus.indicator.thickness": "md.sys.state.focus-indicator.thickness", "md.comp.outlined-card.focus.outline.color": "onSurface", "md.comp.outlined-card.focus.state-layer.color": "onSurface", "md.comp.outlined-card.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.outlined-card.hover.container.elevation": "md.sys.elevation.level1", "md.comp.outlined-card.hover.outline.color": "outlineVariant", "md.comp.outlined-card.hover.state-layer.color": "onSurface", "md.comp.outlined-card.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.outlined-card.icon.color": "primary", "md.comp.outlined-card.icon.size": 24.0, "md.comp.outlined-card.outline.color": "outlineVariant", "md.comp.outlined-card.outline.width": 1.0, "md.comp.outlined-card.pressed.container.elevation": "md.sys.elevation.level0", "md.comp.outlined-card.pressed.outline.color": "outlineVariant", "md.comp.outlined-card.pressed.state-layer.color": "onSurface", "md.comp.outlined-card.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity"}