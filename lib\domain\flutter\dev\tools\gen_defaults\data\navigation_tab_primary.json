{"version": "6_1_0", "md.comp.primary-navigation-tab.active.focus.state-layer.color": "primary", "md.comp.primary-navigation-tab.active.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.primary-navigation-tab.active.hover.state-layer.color": "primary", "md.comp.primary-navigation-tab.active.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.primary-navigation-tab.active-indicator.color": "primary", "md.comp.primary-navigation-tab.active-indicator.height": 3.0, "md.comp.primary-navigation-tab.active.pressed.state-layer.color": "primary", "md.comp.primary-navigation-tab.active.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.primary-navigation-tab.container.color": "surface", "md.comp.primary-navigation-tab.container.elevation": "md.sys.elevation.level0", "md.comp.primary-navigation-tab.container.height": 48.0, "md.comp.primary-navigation-tab.container.shape": "md.sys.shape.corner.none", "md.comp.primary-navigation-tab.focus.indicator.color": "secondary", "md.comp.primary-navigation-tab.focus.indicator.outline.offset": "md.sys.state.focus-indicator.inner-offset", "md.comp.primary-navigation-tab.focus.indicator.thickness": "md.sys.state.focus-indicator.thickness", "md.comp.primary-navigation-tab.inactive.focus.state-layer.color": "onSurface", "md.comp.primary-navigation-tab.inactive.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.primary-navigation-tab.inactive.hover.state-layer.color": "onSurface", "md.comp.primary-navigation-tab.inactive.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.primary-navigation-tab.inactive.pressed.state-layer.color": "primary", "md.comp.primary-navigation-tab.inactive.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.primary-navigation-tab.with-icon.active.focus.icon.color": "primary", "md.comp.primary-navigation-tab.with-icon.active.hover.icon.color": "primary", "md.comp.primary-navigation-tab.with-icon.active.icon.color": "primary", "md.comp.primary-navigation-tab.with-icon.active.pressed.icon.color": "primary", "md.comp.primary-navigation-tab.with-icon-and-label-text.container.height": 64.0, "md.comp.primary-navigation-tab.with-icon.icon.size": 24.0, "md.comp.primary-navigation-tab.with-icon.inactive.focus.icon.color": "onSurface", "md.comp.primary-navigation-tab.with-icon.inactive.hover.icon.color": "onSurface", "md.comp.primary-navigation-tab.with-icon.inactive.icon.color": "onSurfaceVariant", "md.comp.primary-navigation-tab.with-icon.inactive.pressed.icon.color": "onSurface", "md.comp.primary-navigation-tab.with-label-text.active.focus.label-text.color": "primary", "md.comp.primary-navigation-tab.with-label-text.active.hover.label-text.color": "primary", "md.comp.primary-navigation-tab.with-label-text.active.label-text.color": "primary", "md.comp.primary-navigation-tab.with-label-text.active.pressed.label-text.color": "primary", "md.comp.primary-navigation-tab.with-label-text.inactive.focus.label-text.color": "onSurface", "md.comp.primary-navigation-tab.with-label-text.inactive.hover.label-text.color": "onSurface", "md.comp.primary-navigation-tab.with-label-text.inactive.label-text.color": "onSurfaceVariant", "md.comp.primary-navigation-tab.with-label-text.inactive.pressed.label-text.color": "onSurface", "md.comp.primary-navigation-tab.with-label-text.label-text.text-style": "titleSmall"}