{"version": "6_1_0", "md.comp.assist-chip.container.height": 32.0, "md.comp.assist-chip.container.shape": "md.sys.shape.corner.small", "md.comp.assist-chip.disabled.label-text.color": "onSurface", "md.comp.assist-chip.disabled.label-text.opacity": 0.38, "md.comp.assist-chip.dragged.container.elevation": "md.sys.elevation.level4", "md.comp.assist-chip.dragged.label-text.color": "onSurface", "md.comp.assist-chip.dragged.state-layer.color": "onSurface", "md.comp.assist-chip.dragged.state-layer.opacity": "md.sys.state.dragged.state-layer-opacity", "md.comp.assist-chip.elevated.container.color": "surfaceContainerLow", "md.comp.assist-chip.elevated.container.elevation": "md.sys.elevation.level1", "md.comp.assist-chip.elevated.container.shadow-color": "shadow", "md.comp.assist-chip.elevated.disabled.container.color": "onSurface", "md.comp.assist-chip.elevated.disabled.container.elevation": "md.sys.elevation.level0", "md.comp.assist-chip.elevated.disabled.container.opacity": 0.12, "md.comp.assist-chip.elevated.focus.container.elevation": "md.sys.elevation.level1", "md.comp.assist-chip.elevated.hover.container.elevation": "md.sys.elevation.level2", "md.comp.assist-chip.elevated.pressed.container.elevation": "md.sys.elevation.level1", "md.comp.assist-chip.flat.container.elevation": "md.sys.elevation.level0", "md.comp.assist-chip.flat.disabled.outline.color": "onSurface", "md.comp.assist-chip.flat.disabled.outline.opacity": 0.12, "md.comp.assist-chip.flat.focus.outline.color": "onSurface", "md.comp.assist-chip.flat.outline.color": "outlineVariant", "md.comp.assist-chip.flat.outline.width": 1.0, "md.comp.assist-chip.focus.indicator.color": "secondary", "md.comp.assist-chip.focus.indicator.outline.offset": "md.sys.state.focus-indicator.outer-offset", "md.comp.assist-chip.focus.indicator.thickness": "md.sys.state.focus-indicator.thickness", "md.comp.assist-chip.focus.label-text.color": "onSurface", "md.comp.assist-chip.focus.state-layer.color": "onSurface", "md.comp.assist-chip.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.assist-chip.hover.label-text.color": "onSurface", "md.comp.assist-chip.hover.state-layer.color": "onSurface", "md.comp.assist-chip.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.assist-chip.label-text.color": "onSurface", "md.comp.assist-chip.label-text.text-style": "labelLarge", "md.comp.assist-chip.pressed.label-text.color": "onSurface", "md.comp.assist-chip.pressed.state-layer.color": "onSurface", "md.comp.assist-chip.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.assist-chip.with-icon.disabled.icon.color": "onSurface", "md.comp.assist-chip.with-icon.disabled.icon.opacity": 0.38, "md.comp.assist-chip.with-icon.dragged.icon.color": "primary", "md.comp.assist-chip.with-icon.focus.icon.color": "primary", "md.comp.assist-chip.with-icon.hover.icon.color": "primary", "md.comp.assist-chip.with-icon.icon.color": "primary", "md.comp.assist-chip.with-icon.icon.size": 18.0, "md.comp.assist-chip.with-icon.pressed.icon.color": "primary"}