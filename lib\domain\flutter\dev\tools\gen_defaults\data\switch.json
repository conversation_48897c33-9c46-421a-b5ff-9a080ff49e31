{"version": "6_1_0", "md.comp.switch.disabled.selected.handle.color": "surface", "md.comp.switch.disabled.selected.handle.opacity": 1.0, "md.comp.switch.disabled.selected.icon.color": "onSurface", "md.comp.switch.disabled.selected.icon.opacity": 0.38, "md.comp.switch.disabled.selected.track.color": "onSurface", "md.comp.switch.disabled.track.opacity": 0.12, "md.comp.switch.disabled.unselected.handle.color": "onSurface", "md.comp.switch.disabled.unselected.handle.opacity": 0.38, "md.comp.switch.disabled.unselected.icon.color": "surfaceContainerHighest", "md.comp.switch.disabled.unselected.icon.opacity": 0.38, "md.comp.switch.disabled.unselected.track.color": "surfaceContainerHighest", "md.comp.switch.disabled.unselected.track.outline.color": "onSurface", "md.comp.switch.focus.indicator.color": "secondary", "md.comp.switch.focus.indicator.offset": "md.sys.state.focus-indicator.outer-offset", "md.comp.switch.focus.indicator.thickness": "md.sys.state.focus-indicator.thickness", "md.comp.switch.handle.shape": "md.sys.shape.corner.full", "md.comp.switch.pressed.handle.height": 28.0, "md.comp.switch.pressed.handle.width": 28.0, "md.comp.switch.selected.focus.handle.color": "primaryContainer", "md.comp.switch.selected.focus.icon.color": "onPrimaryContainer", "md.comp.switch.selected.focus.state-layer.color": "primary", "md.comp.switch.selected.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.switch.selected.focus.track.color": "primary", "md.comp.switch.selected.handle.color": "onPrimary", "md.comp.switch.selected.handle.height": 24.0, "md.comp.switch.selected.handle.width": 24.0, "md.comp.switch.selected.hover.handle.color": "primaryContainer", "md.comp.switch.selected.hover.icon.color": "onPrimaryContainer", "md.comp.switch.selected.hover.state-layer.color": "primary", "md.comp.switch.selected.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.switch.selected.hover.track.color": "primary", "md.comp.switch.selected.icon.color": "onPrimaryContainer", "md.comp.switch.selected.icon.size": 16.0, "md.comp.switch.selected.pressed.handle.color": "primaryContainer", "md.comp.switch.selected.pressed.icon.color": "onPrimaryContainer", "md.comp.switch.selected.pressed.state-layer.color": "primary", "md.comp.switch.selected.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.switch.selected.pressed.track.color": "primary", "md.comp.switch.selected.track.color": "primary", "md.comp.switch.state-layer.shape": "md.sys.shape.corner.full", "md.comp.switch.state-layer.size": 40.0, "md.comp.switch.track.height": 32.0, "md.comp.switch.track.outline.width": 2.0, "md.comp.switch.track.shape": "md.sys.shape.corner.full", "md.comp.switch.track.width": 52.0, "md.comp.switch.unselected.focus.handle.color": "onSurfaceVariant", "md.comp.switch.unselected.focus.icon.color": "surfaceContainerHighest", "md.comp.switch.unselected.focus.state-layer.color": "onSurface", "md.comp.switch.unselected.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.switch.unselected.focus.track.color": "surfaceContainerHighest", "md.comp.switch.unselected.focus.track.outline.color": "outline", "md.comp.switch.unselected.handle.color": "outline", "md.comp.switch.unselected.handle.height": 16.0, "md.comp.switch.unselected.handle.width": 16.0, "md.comp.switch.unselected.hover.handle.color": "onSurfaceVariant", "md.comp.switch.unselected.hover.icon.color": "surfaceContainerHighest", "md.comp.switch.unselected.hover.state-layer.color": "onSurface", "md.comp.switch.unselected.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.switch.unselected.hover.track.color": "surfaceContainerHighest", "md.comp.switch.unselected.hover.track.outline.color": "outline", "md.comp.switch.unselected.icon.color": "surfaceContainerHighest", "md.comp.switch.unselected.icon.size": 16.0, "md.comp.switch.unselected.pressed.handle.color": "onSurfaceVariant", "md.comp.switch.unselected.pressed.icon.color": "surfaceContainerHighest", "md.comp.switch.unselected.pressed.state-layer.color": "onSurface", "md.comp.switch.unselected.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.switch.unselected.pressed.track.color": "surfaceContainerHighest", "md.comp.switch.unselected.pressed.track.outline.color": "outline", "md.comp.switch.unselected.track.color": "surfaceContainerHighest", "md.comp.switch.unselected.track.outline.color": "outline", "md.comp.switch.with-icon.handle.height": 24.0, "md.comp.switch.with-icon.handle.width": 24.0}