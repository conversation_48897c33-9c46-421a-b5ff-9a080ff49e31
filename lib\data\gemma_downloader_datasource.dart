import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter_gemma/flutter_gemma.dart';
import 'package:offline_menu_translator/domain/download_model.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class GemmaDownloaderDataSource {
  final DownloadModel model;
  late final ModelFileManager _modelManager;

  GemmaDownloaderDataSource({required this.model}) {
    _modelManager = FlutterGemmaPlugin.instance.modelManager;
  }

  String get _preferenceKey => 'model_downloaded_${model.modelFilename}';

  Future<String> getFilePath() async {
    final directory = await getApplicationDocumentsDirectory();
    return '${directory.path}/${model.modelFilename}';
  }

  Future<bool> checkModelExistence() async {
    try {
      // Check if model is already downloaded using flutter_gemma
      final filePath = await getFilePath();
      final file = File(filePath);

      if (file.existsSync()) {
        // Set the model path in flutter_gemma
        await _modelManager.setModelPath(model.modelFilename);
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('Error checking model existence: $e');
      return false;
    }
  }

  /// Downloads the model file using flutter_gemma and tracks progress.
  Future<void> downloadModel({
    required String token,
    required Function(double) onProgress,
  }) async {
    try {
      debugPrint('Starting download for model: ${model.modelUrl}');

      // Use flutter_gemma's ModelFileManager to download the model
      final downloadStream = _modelManager.downloadModelFromNetworkWithProgress(
        model.modelUrl,
      );

      await for (final progress in downloadStream) {
        onProgress(progress / 100.0); // Convert percentage to 0-1 range
        debugPrint('Download progress: $progress%');
      }

      // Set the model path after successful download
      await _modelManager.setModelPath(model.modelFilename);

      // Mark as downloaded in preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_preferenceKey, true);

      debugPrint('Model download completed successfully');
    } catch (e) {
      debugPrint('Error downloading model: $e');

      // Mark as not downloaded on error
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_preferenceKey, false);

      rethrow;
    }
  }

  /// Creates an inference model from the downloaded model
  Future<InferenceModel> createInferenceModel({
    bool supportImage = false,
    int maxTokens = 2048,
    PreferredBackend backend = PreferredBackend.gpu,
  }) async {
    try {
      final gemma = FlutterGemmaPlugin.instance;

      final inferenceModel = await gemma.createModel(
        modelType: ModelType.gemmaIt,
        preferredBackend: backend,
        maxTokens: maxTokens,
        supportImage: supportImage,
        maxNumImages: supportImage ? 1 : 0,
      );

      return inferenceModel;
    } catch (e) {
      debugPrint('Error creating inference model: $e');
      rethrow;
    }
  }

  /// Creates a chat instance from the inference model
  Future<InferenceChat> createChat({
    required InferenceModel inferenceModel,
    bool supportImage = false,
    double temperature = 0.8,
    int randomSeed = 1,
    int topK = 1,
  }) async {
    try {
      final chat = await inferenceModel.createChat(
        temperature: temperature,
        randomSeed: randomSeed,
        topK: topK,
        supportImage: supportImage,
      );

      return chat;
    } catch (e) {
      debugPrint('Error creating chat: $e');
      rethrow;
    }
  }
}
