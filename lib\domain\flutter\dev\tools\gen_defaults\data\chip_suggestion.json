{"version": "6_1_0", "md.comp.suggestion-chip.container.height": 32.0, "md.comp.suggestion-chip.container.shape": "md.sys.shape.corner.small", "md.comp.suggestion-chip.disabled.label-text.color": "onSurface", "md.comp.suggestion-chip.disabled.label-text.opacity": 0.38, "md.comp.suggestion-chip.dragged.container.elevation": "md.sys.elevation.level4", "md.comp.suggestion-chip.dragged.label-text.color": "onSurfaceVariant", "md.comp.suggestion-chip.dragged.state-layer.color": "onSurfaceVariant", "md.comp.suggestion-chip.dragged.state-layer.opacity": "md.sys.state.dragged.state-layer-opacity", "md.comp.suggestion-chip.elevated.container.color": "surfaceContainerLow", "md.comp.suggestion-chip.elevated.container.elevation": "md.sys.elevation.level1", "md.comp.suggestion-chip.elevated.container.shadow-color": "shadow", "md.comp.suggestion-chip.elevated.disabled.container.color": "onSurface", "md.comp.suggestion-chip.elevated.disabled.container.elevation": "md.sys.elevation.level0", "md.comp.suggestion-chip.elevated.disabled.container.opacity": 0.12, "md.comp.suggestion-chip.elevated.focus.container.elevation": "md.sys.elevation.level1", "md.comp.suggestion-chip.elevated.hover.container.elevation": "md.sys.elevation.level2", "md.comp.suggestion-chip.elevated.pressed.container.elevation": "md.sys.elevation.level1", "md.comp.suggestion-chip.flat.container.elevation": "md.sys.elevation.level0", "md.comp.suggestion-chip.flat.disabled.outline.color": "onSurface", "md.comp.suggestion-chip.flat.disabled.outline.opacity": 0.12, "md.comp.suggestion-chip.flat.focus.outline.color": "onSurfaceVariant", "md.comp.suggestion-chip.flat.outline.color": "outlineVariant", "md.comp.suggestion-chip.flat.outline.width": 1.0, "md.comp.suggestion-chip.focus.indicator.color": "secondary", "md.comp.suggestion-chip.focus.indicator.outline.offset": "md.sys.state.focus-indicator.outer-offset", "md.comp.suggestion-chip.focus.indicator.thickness": "md.sys.state.focus-indicator.thickness", "md.comp.suggestion-chip.focus.label-text.color": "onSurfaceVariant", "md.comp.suggestion-chip.focus.state-layer.color": "onSurfaceVariant", "md.comp.suggestion-chip.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.suggestion-chip.hover.label-text.color": "onSurfaceVariant", "md.comp.suggestion-chip.hover.state-layer.color": "onSurfaceVariant", "md.comp.suggestion-chip.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.suggestion-chip.label-text.color": "onSurfaceVariant", "md.comp.suggestion-chip.label-text.text-style": "labelLarge", "md.comp.suggestion-chip.pressed.label-text.color": "onSurfaceVariant", "md.comp.suggestion-chip.pressed.state-layer.color": "onSurfaceVariant", "md.comp.suggestion-chip.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.suggestion-chip.with-leading-icon.disabled.leading-icon.color": "onSurface", "md.comp.suggestion-chip.with-leading-icon.disabled.leading-icon.opacity": 0.38, "md.comp.suggestion-chip.with-leading-icon.dragged.leading-icon.color": "primary", "md.comp.suggestion-chip.with-leading-icon.focus.leading-icon.color": "primary", "md.comp.suggestion-chip.with-leading-icon.hover.leading-icon.color": "primary", "md.comp.suggestion-chip.with-leading-icon.leading-icon.color": "primary", "md.comp.suggestion-chip.with-leading-icon.leading-icon.size": 18.0, "md.comp.suggestion-chip.with-leading-icon.pressed.leading-icon.color": "primary"}