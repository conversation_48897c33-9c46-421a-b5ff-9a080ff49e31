{"version": "6_1_0", "md.comp.outlined-text-field.caret.color": "primary", "md.comp.outlined-text-field.container.shape": "md.sys.shape.corner.extra-small", "md.comp.outlined-text-field.disabled.input-text.color": "onSurface", "md.comp.outlined-text-field.disabled.input-text.opacity": 0.38, "md.comp.outlined-text-field.disabled.label-text.color": "onSurface", "md.comp.outlined-text-field.disabled.label-text.opacity": 0.38, "md.comp.outlined-text-field.disabled.leading-icon.color": "onSurface", "md.comp.outlined-text-field.disabled.leading-icon.opacity": 0.38, "md.comp.outlined-text-field.disabled.outline.color": "onSurface", "md.comp.outlined-text-field.disabled.outline.opacity": 0.12, "md.comp.outlined-text-field.disabled.outline.width": 1.0, "md.comp.outlined-text-field.disabled.supporting-text.color": "onSurface", "md.comp.outlined-text-field.disabled.supporting-text.opacity": 0.38, "md.comp.outlined-text-field.disabled.trailing-icon.color": "onSurface", "md.comp.outlined-text-field.disabled.trailing-icon.opacity": 0.38, "md.comp.outlined-text-field.error.focus.caret.color": "error", "md.comp.outlined-text-field.error.focus.indicator.outline.color": "error", "md.comp.outlined-text-field.error.focus.input-text.color": "onSurface", "md.comp.outlined-text-field.error.focus.label-text.color": "error", "md.comp.outlined-text-field.error.focus.leading-icon.color": "onSurfaceVariant", "md.comp.outlined-text-field.error.focus.outline.color": "error", "md.comp.outlined-text-field.error.focus.supporting-text.color": "error", "md.comp.outlined-text-field.error.focus.trailing-icon.color": "error", "md.comp.outlined-text-field.error.hover.input-text.color": "onSurface", "md.comp.outlined-text-field.error.hover.label-text.color": "onError<PERSON><PERSON>r", "md.comp.outlined-text-field.error.hover.leading-icon.color": "onSurfaceVariant", "md.comp.outlined-text-field.error.hover.outline.color": "onError<PERSON><PERSON>r", "md.comp.outlined-text-field.error.hover.supporting-text.color": "error", "md.comp.outlined-text-field.error.hover.trailing-icon.color": "onError<PERSON><PERSON>r", "md.comp.outlined-text-field.error.input-text.color": "onSurface", "md.comp.outlined-text-field.error.label-text.color": "error", "md.comp.outlined-text-field.error.leading-icon.color": "onSurfaceVariant", "md.comp.outlined-text-field.error.outline.color": "error", "md.comp.outlined-text-field.error.supporting-text.color": "error", "md.comp.outlined-text-field.error.trailing-icon.color": "error", "md.comp.outlined-text-field.focus.indicator.outline.color": "secondary", "md.comp.outlined-text-field.focus.indicator.outline.thickness": "md.sys.state.focus-indicator.thickness", "md.comp.outlined-text-field.focus.input-text.color": "onSurface", "md.comp.outlined-text-field.focus.label-text.color": "primary", "md.comp.outlined-text-field.focus.leading-icon.color": "onSurfaceVariant", "md.comp.outlined-text-field.focus.outline.color": "primary", "md.comp.outlined-text-field.focus.outline.width": 2.0, "md.comp.outlined-text-field.focus.supporting-text.color": "onSurfaceVariant", "md.comp.outlined-text-field.focus.trailing-icon.color": "onSurfaceVariant", "md.comp.outlined-text-field.hover.input-text.color": "onSurface", "md.comp.outlined-text-field.hover.label-text.color": "onSurface", "md.comp.outlined-text-field.hover.leading-icon.color": "onSurfaceVariant", "md.comp.outlined-text-field.hover.outline.color": "onSurface", "md.comp.outlined-text-field.hover.outline.width": 1.0, "md.comp.outlined-text-field.hover.supporting-text.color": "onSurfaceVariant", "md.comp.outlined-text-field.hover.trailing-icon.color": "onSurfaceVariant", "md.comp.outlined-text-field.input-text.color": "onSurface", "md.comp.outlined-text-field.input-text.text-style": "bodyLarge", "md.comp.outlined-text-field.input-text.placeholder.color": "onSurfaceVariant", "md.comp.outlined-text-field.input-text.prefix.color": "onSurfaceVariant", "md.comp.outlined-text-field.input-text.suffix.color": "onSurfaceVariant", "md.comp.outlined-text-field.label-text.color": "onSurfaceVariant", "md.comp.outlined-text-field.label-text.text-style": "bodyLarge", "md.comp.outlined-text-field.leading-icon.color": "onSurfaceVariant", "md.comp.outlined-text-field.leading-icon.size": 24.0, "md.comp.outlined-text-field.outline.color": "outline", "md.comp.outlined-text-field.outline.width": 1.0, "md.comp.outlined-text-field.supporting-text.color": "onSurfaceVariant", "md.comp.outlined-text-field.supporting-text.text-style": "bodySmall", "md.comp.outlined-text-field.trailing-icon.color": "onSurfaceVariant", "md.comp.outlined-text-field.trailing-icon.size": 24.0}