<!DOCTYPE HTML>
<!-- Copyright 2014 The Flutter Authors. All rights reserved.
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file. -->
<html>
<head>
  <meta charset="UTF-8">

  <title>Integration test. App load with flutter.js and onEntrypointLoaded API. nonce required.</title>

  <!-- Enable a CSP that requires a nonce for script and style-src. -->
  <meta http-equiv="Content-Security-Policy" content="script-src 'nonce-SOME_NONCE' 'wasm-unsafe-eval'; font-src https://fonts.gstatic.com; style-src 'nonce-SOME_NONCE'; worker-src 'self';">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="Web Test">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>
  <link rel="manifest" href="manifest.json">
  <script nonce="SOME_NONCE">
    // The value below is injected by flutter build, do not touch.
    var serviceWorkerVersion = null;
  </script>
  <!-- This script adds the flutter initialization JS code -->
  <script nonce="SOME_NONCE" src="flutter.js" defer></script>
</head>
<body>
  <script nonce="SOME_NONCE">
    window.addEventListener('load', function(ev) {
      // Download main.dart.js
      _flutter.loader.loadEntrypoint({
        nonce: 'SOME_NONCE',
        onEntrypointLoaded: onEntrypointLoaded,
        serviceWorker: {
          serviceWorkerVersion: serviceWorkerVersion,
        }
      });

      // Once the entrypoint is ready, do things!
      async function onEntrypointLoaded(engineInitializer) {
        const appRunner = await engineInitializer.initializeEngine({
          nonce: 'SOME_NONCE',
        });
        appRunner.runApp();
      }
    });
  </script>
</body>
</html>
