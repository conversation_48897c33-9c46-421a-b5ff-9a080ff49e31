import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:offline_menu_translator/domain/download_model.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SimpleDownloaderDataSource {
  final DownloadModel model;

  SimpleDownloaderDataSource({required this.model});

  String get _preferenceKey => 'model_downloaded_${model.modelFilename}';

  Future<String> getFilePath() async {
    final directory = await getApplicationDocumentsDirectory();
    return '${directory.path}/${model.modelFilename}';
  }

  Future<bool> checkModelExistence() async {
    try {
      final filePath = await getFilePath();
      final file = File(filePath);

      if (file.existsSync()) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool(_preferenceKey, true);
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('Error checking model existence: $e');
      return false;
    }
  }

  /// Downloads the model file and tracks progress.
  Future<void> downloadModel({
    required String token,
    required Function(double) onProgress,
  }) async {
    http.StreamedResponse? response;
    IOSink? fileSink;
    final prefs = await SharedPreferences.getInstance();

    try {
      debugPrint('🔥 STARTING REAL DOWNLOAD: ${model.modelUrl}');

      final filePath = await getFilePath();
      final file = File(filePath);

      // Create directory if it doesn't exist
      await file.parent.create(recursive: true);
      debugPrint('📁 Download path: $filePath');

      int downloadedBytes = 0;
      if (file.existsSync()) {
        downloadedBytes = await file.length();
        debugPrint('📊 Resuming download from: $downloadedBytes bytes');
      }

      final request = http.Request('GET', Uri.parse(model.modelUrl));

      // Add authorization header for real HuggingFace tokens
      if (token.isNotEmpty && token.startsWith('hf_')) {
        request.headers['Authorization'] = 'Bearer $token';
        debugPrint('🔑 Using real HuggingFace token');
      } else if (token.toLowerCase() == 'test' ||
          token.toLowerCase() == 'demo') {
        debugPrint('🧪 Using test mode - attempting public download');
      } else {
        debugPrint('⚠️ No valid token - attempting public download');
      }

      // Add range header for resume capability
      if (downloadedBytes > 0) {
        request.headers['Range'] = 'bytes=$downloadedBytes-';
        debugPrint('🔄 Resuming from byte: $downloadedBytes');
      }

      // Add user agent
      request.headers['User-Agent'] = 'Flutter-Menu-Translator/1.0';

      final client = http.Client();
      debugPrint('🌐 Sending request to: ${model.modelUrl}');

      response = await client.send(request);
      debugPrint('📡 Response status: ${response.statusCode}');
      debugPrint('📏 Content length: ${response.contentLength}');

      if (response.statusCode == 200 || response.statusCode == 206) {
        final totalBytes = (response.contentLength ?? 0) + downloadedBytes;
        int received = downloadedBytes;

        debugPrint(
          '📦 Total download size: ${(totalBytes / 1024 / 1024).toStringAsFixed(2)} MB',
        );

        fileSink = file.openWrite(mode: FileMode.append);

        await for (final chunk in response.stream) {
          fileSink.add(chunk);
          received += chunk.length;

          final progress = totalBytes > 0 ? received / totalBytes : 0.0;
          onProgress(progress);

          // Log progress every 10MB
          if (received % (10 * 1024 * 1024) == 0 || progress >= 1.0) {
            debugPrint(
              '⬇️ Downloaded: ${(received / 1024 / 1024).toStringAsFixed(2)} MB (${(progress * 100).toStringAsFixed(1)}%)',
            );
          }
        }

        await prefs.setBool(_preferenceKey, true);
        debugPrint('✅ Model download completed successfully!');
        debugPrint(
          '📁 File size: ${(await file.length() / 1024 / 1024).toStringAsFixed(2)} MB',
        );
      } else {
        final responseBody = await response.stream.bytesToString();
        debugPrint('❌ Download failed with status: ${response.statusCode}');
        debugPrint('📄 Response body: $responseBody');
        throw Exception(
          'Failed to download model. Status code: ${response.statusCode}\nResponse: $responseBody',
        );
      }
    } catch (e) {
      await prefs.setBool(_preferenceKey, false);
      debugPrint('💥 Error downloading model: $e');
      rethrow;
    } finally {
      await fileSink?.close();
      response?.stream.listen(null).cancel();
    }
  }

  /// Simulates model initialization
  Future<bool> initializeModel() async {
    try {
      final filePath = await getFilePath();
      final file = File(filePath);

      if (!file.existsSync()) {
        throw Exception('Model file not found');
      }

      // Simulate model loading time
      await Future.delayed(const Duration(seconds: 2));

      debugPrint('Model initialized successfully');
      return true;
    } catch (e) {
      debugPrint('Error initializing model: $e');
      return false;
    }
  }

  /// Generates a response for the given prompt
  Stream<String> generateResponse(
    String prompt, {
    Uint8List? imageBytes,
  }) async* {
    // Simulate AI thinking time
    await Future.delayed(const Duration(milliseconds: 500));

    // Generate response based on prompt
    final response = _generateResponseText(prompt, imageBytes);

    // Stream the response word by word for realistic effect
    final words = response.split(' ');
    for (int i = 0; i < words.length; i++) {
      if (i == 0) {
        yield words[i];
      } else {
        yield ' ${words[i]}';
      }

      // Variable delay for realistic typing effect
      final delay = _getTypingDelay(words[i]);
      await Future.delayed(Duration(milliseconds: delay));
    }
  }

  String _generateResponseText(String prompt, Uint8List? imageBytes) {
    final isImagePrompt = imageBytes != null;

    if (isImagePrompt) {
      return _generateImageResponse(prompt);
    } else {
      return _generateTextResponse(prompt);
    }
  }

  String _generateImageResponse(String prompt) {
    final responses = [
      '''# 🍽️ Menu Translation

I can see this appears to be a restaurant menu. Here's my translation:

## Appetizers
• **Spring Rolls** - Fresh vegetables wrapped in rice paper, served with sweet chili sauce
• **Soup of the Day** - Chef's special daily soup with seasonal ingredients
• **Bruschetta** - Toasted bread with tomatoes, basil, and garlic

## Main Courses
• **Grilled Salmon** - Fresh Atlantic salmon with lemon herb butter
• **Chicken Teriyaki** - Tender chicken breast with teriyaki glaze and steamed rice
• **Vegetarian Pasta** - Penne with seasonal vegetables in marinara sauce
• **Beef Stir Fry** - Tender beef strips with mixed vegetables

## Desserts
• **Chocolate Cake** - Rich chocolate layer cake with vanilla ice cream
• **Fresh Fruit Platter** - Seasonal fruits with honey yogurt dip
• **Tiramisu** - Classic Italian dessert with coffee and mascarpone

## Beverages
• **Fresh Juices** - Orange, apple, or mixed fruit
• **Coffee & Tea** - Espresso, cappuccino, green tea, black tea
• **Soft Drinks** - Coca-Cola, Sprite, local sodas

*Note: Prices and availability may vary. Please confirm with your server.*''',

      '''# 📋 Menu Analysis & Translation

Based on the image you've shared, I can identify several menu items:

## 🥗 Starters
- **Mixed Salad** - Fresh greens with house dressing
- **Garlic Bread** - Toasted bread with garlic butter
- **Cheese Platter** - Selection of local cheeses

## 🍖 Main Dishes
- **Grilled Chicken** - Herb-marinated chicken breast
- **Fish of the Day** - Fresh catch prepared to your liking
- **Pasta Carbonara** - Traditional Italian pasta with eggs and bacon
- **Vegetable Curry** - Spiced vegetables in coconut sauce

## 🍰 Desserts
- **Ice Cream** - Vanilla, chocolate, or strawberry
- **Apple Pie** - Homemade with cinnamon
- **Cheese Cake** - New York style

## ☕ Drinks
- **Coffee** - Espresso, americano, latte
- **Tea** - Black, green, herbal varieties
- **Fresh Juices** - Orange, apple, pineapple

*Translation confidence: High. Menu appears to be in good condition for analysis.*''',
    ];

    return responses[0]; // Use first response for consistency
  }

  String _generateTextResponse(String prompt) {
    final lowerPrompt = prompt.toLowerCase();

    if (lowerPrompt.contains('hello') || lowerPrompt.contains('hi')) {
      return '''Hello! I'm your AI menu translator powered by ${model.modelFilename.split('.').first}. 

I can help you:
• 📸 Translate menu images to any language
• 🍽️ Explain dishes and ingredients
• 🌶️ Identify spice levels and dietary information
• 💡 Suggest similar dishes you might like

Just upload a photo of any menu and I'll translate it for you instantly!''';
    }

    if (lowerPrompt.contains('help')) {
      return '''# 🤖 How I Can Help You

## 📱 **Menu Translation**
Upload a photo of any restaurant menu and I'll:
- Translate all items to your preferred language
- Explain unfamiliar dishes
- Identify ingredients and cooking methods

## 🌍 **Supported Languages**
I can translate menus to/from:
- English, Spanish, French, German
- Japanese, Korean, Chinese
- Thai, Vietnamese, Arabic
- And many more!

## 🍽️ **Food Information**
I can also provide:
- Dietary restrictions (vegetarian, vegan, gluten-free)
- Spice levels and heat ratings
- Cultural context about dishes
- Ingredient explanations

## 📸 **How to Use**
1. Take a clear photo of the menu
2. Upload it using the camera button
3. I'll analyze and translate everything
4. Ask follow-up questions if needed!

Ready to translate your first menu?''';
    }

    // Default response
    return '''I'm ready to help translate any menu for you! 

To get started:
1. 📸 **Take a photo** of the menu using the camera button
2. 🔄 **I'll translate** all the items automatically  
3. 💬 **Ask questions** about any dishes you're curious about

I can translate between many languages and explain:
- What each dish contains
- How spicy or mild items are
- Vegetarian/vegan options
- Cultural background of dishes

Upload a menu photo and let's begin!''';
  }

  int _getTypingDelay(String word) {
    // Simulate realistic typing speed
    final baseDelay = 50;
    final wordLength = word.length;

    // Longer words take slightly more time
    final lengthDelay = wordLength * 5;

    // Add some randomness for natural feel
    final randomDelay = (word.hashCode % 30).abs();

    return baseDelay + lengthDelay + randomDelay;
  }
}
