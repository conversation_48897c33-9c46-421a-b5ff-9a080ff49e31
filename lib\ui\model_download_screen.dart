import 'package:flutter/material.dart';
import 'package:offline_menu_translator/data/real_gemma_datasource.dart';
import 'package:offline_menu_translator/domain/download_model.dart';
import 'package:offline_menu_translator/ui/model_selection_screen.dart';
import 'package:offline_menu_translator/ui/translator_screen.dart';
import 'package:offline_menu_translator/ui/animations.dart';
import 'package:offline_menu_translator/services/model_storage_service.dart';

class ModelDownloadScreen extends StatefulWidget {
  final ModelInfo model;
  final String token;

  const ModelDownloadScreen({
    super.key,
    required this.model,
    required this.token,
  });

  @override
  State<ModelDownloadScreen> createState() => _ModelDownloadScreenState();
}

class _ModelDownloadScreenState extends State<ModelDownloadScreen>
    with TickerProviderStateMixin {
  late RealGemmaDataSource _gemmaDataSource;

  double _downloadProgress = 0.0;
  String _downloadStatus = 'Preparing download...';
  bool _isDownloading = false;
  bool _isCompleted = false;
  bool _hasError = false;
  String? _errorMessage;

  late AnimationController _progressAnimationController;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();

    _progressAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _progressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _progressAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    _initializeDownloader();
    _startDownload();
  }

  void _initializeDownloader() {
    _gemmaDataSource = RealGemmaDataSource(
      model: DownloadModel(
        modelUrl: widget.model.downloadUrl,
        modelFilename: widget.model.filename,
      ),
    );
  }

  Future<void> _startDownload() async {
    setState(() {
      _isDownloading = true;
      _downloadStatus = 'Connecting to HuggingFace Hub...';
    });

    try {
      // Check if model already exists using real flutter_gemma
      final exists = await _gemmaDataSource.checkModelExistence();
      if (exists) {
        setState(() {
          _downloadProgress = 1.0;
          _downloadStatus = 'Model already downloaded!';
          _isCompleted = true;
          _isDownloading = false;
        });
        _progressAnimationController.forward();
        await Future.delayed(const Duration(seconds: 1));
        _navigateToChat();
        return;
      }

      setState(() {
        _downloadStatus = 'Starting real download from HuggingFace...';
      });

      // Real download using flutter_gemma
      await _gemmaDataSource.downloadModel(
        token: widget.token,
        onProgress: (progress) {
          setState(() {
            _downloadProgress = progress;
            _downloadStatus =
                'Downloading... ${(progress * 100).toStringAsFixed(1)}%';
          });
          _progressAnimationController.animateTo(progress);
        },
      );

      // Mark model as downloaded and set as active
      await ModelStorageService.instance.markModelAsDownloaded(widget.model.id);
      await ModelStorageService.instance.setActiveModel(widget.model.id, {
        'name': widget.model.name,
        'description': widget.model.description,
        'size': widget.model.size,
        'capabilities': widget.model.capabilities,
      });

      // Model is now active and ready to use

      setState(() {
        _isCompleted = true;
        _isDownloading = false;
        _downloadStatus = 'Download completed successfully!';
      });

      await Future.delayed(const Duration(seconds: 2));
      _navigateToChat();
    } catch (e) {
      setState(() {
        _hasError = true;
        _isDownloading = false;
        _errorMessage = e.toString();
        _downloadStatus = 'Download failed';
      });
    }
  }

  Future<void> _simulateDownload() async {
    // Simulate realistic download progress
    final totalSteps = 100;
    for (int i = 0; i <= totalSteps; i++) {
      if (!mounted) return;

      final progress = i / totalSteps;
      setState(() {
        _downloadProgress = progress;
        _downloadStatus = _getDownloadStatusMessage(progress);
      });

      if (progress > 0.1 && !_progressAnimationController.isAnimating) {
        _progressAnimationController.forward();
      }

      // Variable delay to simulate real download
      if (i < 10) {
        await Future.delayed(const Duration(milliseconds: 100)); // Fast start
      } else if (i < 80) {
        await Future.delayed(
          const Duration(milliseconds: 50),
        ); // Steady progress
      } else {
        await Future.delayed(
          const Duration(milliseconds: 200),
        ); // Slower finish
      }
    }
  }

  String _getDownloadStatusMessage(double progress) {
    final percentage = (progress * 100).toStringAsFixed(1);

    if (progress < 0.1) {
      return 'Initializing download...';
    } else if (progress < 0.5) {
      return 'Downloading model... $percentage%';
    } else if (progress < 0.9) {
      return 'Almost there... $percentage%';
    } else if (progress < 1.0) {
      return 'Finalizing... $percentage%';
    } else {
      return 'Download completed!';
    }
  }

  void _navigateToChat() {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) =>
            TranslatorScreen(modelInfo: widget.model, token: widget.token),
      ),
    );
  }

  void _retryDownload() {
    setState(() {
      _hasError = false;
      _errorMessage = null;
      _downloadProgress = 0.0;
    });
    _progressAnimationController.reset();
    _startDownload();
  }

  void _goBack() {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(builder: (context) => const ModelSelectionScreen()),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Downloading Model'),
        centerTitle: true,
        leading: _isDownloading
            ? null
            : IconButton(
                onPressed: _goBack,
                icon: const Icon(Icons.arrow_back),
              ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          children: [
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Model info card
                    _buildModelInfoCard(),
                    const SizedBox(height: 48),

                    // Download progress
                    _buildProgressSection(),
                    const SizedBox(height: 32),

                    // Status message
                    _buildStatusMessage(),
                    const SizedBox(height: 24),

                    // Error handling
                    if (_hasError) _buildErrorSection(),
                  ],
                ),
              ),
            ),

            // Action buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildModelInfoCard() {
    final colorScheme = Theme.of(context).colorScheme;

    return FadeInAnimation(
      duration: AppAnimations.medium2,
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Icon(Icons.psychology, size: 48, color: colorScheme.primary),
              const SizedBox(height: 16),
              Text(
                widget.model.name,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: colorScheme.onSurface,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                widget.model.description,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.storage,
                    size: 16,
                    color: colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    widget.model.size,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProgressSection() {
    final colorScheme = Theme.of(context).colorScheme;

    return ScaleInAnimation(
      duration: AppAnimations.medium3,
      child: Column(
        children: [
          // Circular progress indicator
          SizedBox(
            width: 120,
            height: 120,
            child: Stack(
              children: [
                // Background circle
                SizedBox(
                  width: 120,
                  height: 120,
                  child: CircularProgressIndicator(
                    value: 1.0,
                    strokeWidth: 8,
                    color: colorScheme.surfaceContainerHighest,
                  ),
                ),
                // Progress circle
                AnimatedBuilder(
                  animation: _progressAnimation,
                  builder: (context, child) {
                    return SizedBox(
                      width: 120,
                      height: 120,
                      child: CircularProgressIndicator(
                        value: _downloadProgress * _progressAnimation.value,
                        strokeWidth: 8,
                        color: _hasError
                            ? colorScheme.error
                            : _isCompleted
                            ? colorScheme.primary
                            : colorScheme.primary,
                      ),
                    );
                  },
                ),
                // Center content
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (_hasError)
                        Icon(Icons.error, color: colorScheme.error, size: 32)
                      else if (_isCompleted)
                        Icon(
                          Icons.check_circle,
                          color: colorScheme.primary,
                          size: 32,
                        )
                      else
                        Text(
                          '${(_downloadProgress * 100).toStringAsFixed(0)}%',
                          style: Theme.of(context).textTheme.headlineSmall
                              ?.copyWith(
                                color: colorScheme.onSurface,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Linear progress bar
          if (_isDownloading && !_hasError)
            SizedBox(
              width: 200,
              child: LinearProgressIndicator(
                value: _downloadProgress,
                backgroundColor: colorScheme.surfaceContainerHighest,
                valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStatusMessage() {
    final colorScheme = Theme.of(context).colorScheme;

    return FadeInAnimation(
      duration: AppAnimations.short4,
      child: Text(
        _downloadStatus,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          color: _hasError ? colorScheme.error : colorScheme.onSurface,
          fontWeight: FontWeight.w500,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildErrorSection() {
    final colorScheme = Theme.of(context).colorScheme;

    return FadeInAnimation(
      duration: AppAnimations.short4,
      child: Card(
        color: colorScheme.errorContainer,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(
                Icons.error_outline,
                color: colorScheme.onErrorContainer,
                size: 24,
              ),
              const SizedBox(height: 8),
              Text(
                'Download Failed',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: colorScheme.onErrorContainer,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _errorMessage ?? 'Unknown error occurred',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onErrorContainer,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return SafeArea(
      child: Column(
        children: [
          if (_hasError) ...[
            SizedBox(
              width: double.infinity,
              child: FilledButton.icon(
                onPressed: _retryDownload,
                icon: const Icon(Icons.refresh),
                label: const Text('Retry Download'),
                style: FilledButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _goBack,
                icon: const Icon(Icons.arrow_back),
                label: const Text('Choose Different Model'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ] else if (_isCompleted) ...[
            SizedBox(
              width: double.infinity,
              child: FilledButton.icon(
                onPressed: _navigateToChat,
                icon: const Icon(Icons.chat),
                label: const Text('Start Translating'),
                style: FilledButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ] else if (!_isDownloading) ...[
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _goBack,
                icon: const Icon(Icons.arrow_back),
                label: const Text('Back to Model Selection'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  @override
  void dispose() {
    _progressAnimationController.dispose();
    super.dispose();
  }
}
