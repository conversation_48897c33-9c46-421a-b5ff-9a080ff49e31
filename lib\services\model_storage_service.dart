import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class ModelStorageService {
  static const String _downloadedModelsKey = 'downloaded_models';
  static const String _activeModelKey = 'active_model';

  static ModelStorageService? _instance;
  static ModelStorageService get instance {
    _instance ??= ModelStorageService._();
    return _instance!;
  }

  ModelStorageService._();

  /// Check if a model is downloaded
  Future<bool> isModelDownloaded(String modelId) async {
    final prefs = await SharedPreferences.getInstance();
    final downloadedModels = prefs.getStringList(_downloadedModelsKey) ?? [];
    return downloadedModels.contains(modelId);
  }

  /// Mark a model as downloaded
  Future<void> markModelAsDownloaded(String modelId) async {
    final prefs = await SharedPreferences.getInstance();
    final downloadedModels = prefs.getStringList(_downloadedModelsKey) ?? [];
    if (!downloadedModels.contains(modelId)) {
      downloadedModels.add(modelId);
      await prefs.setStringList(_downloadedModelsKey, downloadedModels);
    }
  }

  /// Remove a downloaded model
  Future<void> removeDownloadedModel(String modelId) async {
    final prefs = await SharedPreferences.getInstance();
    final downloadedModels = prefs.getStringList(_downloadedModelsKey) ?? [];
    downloadedModels.remove(modelId);
    await prefs.setStringList(_downloadedModelsKey, downloadedModels);
  }

  /// Get all downloaded models
  Future<List<String>> getDownloadedModels() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getStringList(_downloadedModelsKey) ?? [];
  }

  /// Set active model
  Future<void> setActiveModel(String modelId, Map<String, dynamic> modelInfo) async {
    final prefs = await SharedPreferences.getInstance();
    final modelData = {
      'id': modelId,
      'info': modelInfo,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    await prefs.setString(_activeModelKey, jsonEncode(modelData));
  }

  /// Get active model
  Future<Map<String, dynamic>?> getActiveModel() async {
    final prefs = await SharedPreferences.getInstance();
    final modelDataString = prefs.getString(_activeModelKey);
    if (modelDataString != null) {
      return jsonDecode(modelDataString);
    }
    return null;
  }

  /// Clear active model
  Future<void> clearActiveModel() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_activeModelKey);
  }

  /// Clear all data
  Future<void> clearAllData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_downloadedModelsKey);
    await prefs.remove(_activeModelKey);
  }
}
