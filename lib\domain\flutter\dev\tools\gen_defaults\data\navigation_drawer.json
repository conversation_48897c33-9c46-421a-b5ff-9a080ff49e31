{"version": "6_1_0", "md.comp.navigation-drawer.active.focus.icon.color": "onSecondaryContainer", "md.comp.navigation-drawer.active.focus.label-text.color": "onSecondaryContainer", "md.comp.navigation-drawer.active.focus.state-layer.color": "onSecondaryContainer", "md.comp.navigation-drawer.active.hover.icon.color": "onSecondaryContainer", "md.comp.navigation-drawer.active.hover.label-text.color": "onSecondaryContainer", "md.comp.navigation-drawer.active.hover.state-layer.color": "onSecondaryContainer", "md.comp.navigation-drawer.active.icon.color": "onSecondaryContainer", "md.comp.navigation-drawer.active-indicator.color": "secondaryContainer", "md.comp.navigation-drawer.active-indicator.height": 56.0, "md.comp.navigation-drawer.active-indicator.shape": "md.sys.shape.corner.full", "md.comp.navigation-drawer.active-indicator.width": 336.0, "md.comp.navigation-drawer.active.label-text.color": "onSecondaryContainer", "md.comp.navigation-drawer.active.pressed.icon.color": "onSecondaryContainer", "md.comp.navigation-drawer.active.pressed.label-text.color": "onSecondaryContainer", "md.comp.navigation-drawer.active.pressed.state-layer.color": "onSecondaryContainer", "md.comp.navigation-drawer.bottom.container.shape": "md.sys.shape.corner.large.top", "md.comp.navigation-drawer.container.height": 100.0, "md.comp.navigation-drawer.container.shape": "md.sys.shape.corner.large.end", "md.comp.navigation-drawer.container.width": 360.0, "md.comp.navigation-drawer.focus.indicator.color": "secondary", "md.comp.navigation-drawer.focus.indicator.outline.offset": "md.sys.state.focus-indicator.inner-offset", "md.comp.navigation-drawer.focus.indicator.thickness": "md.sys.state.focus-indicator.thickness", "md.comp.navigation-drawer.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.navigation-drawer.headline.color": "onSurfaceVariant", "md.comp.navigation-drawer.headline.text-style": "titleSmall", "md.comp.navigation-drawer.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.navigation-drawer.icon.size": 24.0, "md.comp.navigation-drawer.inactive.focus.icon.color": "onSurface", "md.comp.navigation-drawer.inactive.focus.label-text.color": "onSurface", "md.comp.navigation-drawer.inactive.focus.state-layer.color": "onSurface", "md.comp.navigation-drawer.inactive.hover.icon.color": "onSurface", "md.comp.navigation-drawer.inactive.hover.label-text.color": "onSurface", "md.comp.navigation-drawer.inactive.hover.state-layer.color": "onSurface", "md.comp.navigation-drawer.inactive.icon.color": "onSurfaceVariant", "md.comp.navigation-drawer.inactive.label-text.color": "onSurfaceVariant", "md.comp.navigation-drawer.inactive.pressed.icon.color": "onSurface", "md.comp.navigation-drawer.inactive.pressed.label-text.color": "onSurface", "md.comp.navigation-drawer.inactive.pressed.state-layer.color": "onSecondaryContainer", "md.comp.navigation-drawer.label-text.text-style": "labelLarge", "md.comp.navigation-drawer.large-badge-label.color": "onSurfaceVariant", "md.comp.navigation-drawer.large-badge-label.text-style": "labelLarge", "md.comp.navigation-drawer.modal.container.color": "surfaceContainerLow", "md.comp.navigation-drawer.modal.container.elevation": "md.sys.elevation.level1", "md.comp.navigation-drawer.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.navigation-drawer.standard.container.color": "surface", "md.comp.navigation-drawer.standard.container.elevation": "md.sys.elevation.level0"}