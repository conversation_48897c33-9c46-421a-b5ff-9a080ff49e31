// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include <map>

// DO NOT EDIT -- DO NOT EDIT -- DO NOT EDIT
// This file is generated by
// flutter/flutter:dev/tools/gen_keycodes/bin/gen_keycodes.dart and should not
// be edited directly.
//
// Edit the template
// flutter/flutter:dev/tools/gen_keycodes/data/glfw_keyboard_map_cc.tmpl
// instead.
//
// See flutter/flutter:dev/tools/gen_keycodes/README.md for more information.

/// Maps GLFW-specific key codes to the matching [LogicalKeyboardKey].
const std::map<int, int> g_glfw_to_logical_key = {
@@@GLFW_KEY_CODE_MAP@@@
};

/// A map of GLFW key codes which have printable representations, but appear
/// on the number pad. Used to provide different key objects for keys like
/// KEY_EQUALS and NUMPAD_EQUALS.
const std::map<int, int> g_glfw_numpad_map = {
@@@GLFW_NUMPAD_MAP@@@
};
