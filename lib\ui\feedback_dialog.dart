import 'package:flutter/material.dart';

class FeedbackDialog extends StatefulWidget {
  const FeedbackDialog({super.key});

  @override
  State<FeedbackDialog> createState() => _FeedbackDialogState();
}

class _FeedbackDialogState extends State<FeedbackDialog> {
  final _feedbackController = TextEditingController();
  bool _isAccurate = true;
  List<String> _selectedIssues = [];

  final List<String> _commonIssues = [
    'Wrong cultural context',
    'Incorrect tonal marks',
    'Missing diacritical marks',
    'Incorrect script rendering',
    'Wrong dish description',
    'Missing dietary information',
  ];

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Translation Feedback'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Is the translation accurate?'),
            Row(
              children: [
                Radio<bool>(
                  value: true,
                  groupValue: _isAccurate,
                  onChanged: (value) {
                    setState(() {
                      _isAccurate = value!;
                    });
                  },
                ),
                const Text('Yes'),
                Radio<bool>(
                  value: false,
                  groupValue: _isAccurate,
                  onChanged: (value) {
                    setState(() {
                      _isAccurate = value!;
                    });
                  },
                ),
                const Text('No'),
              ],
            ),
            if (!_isAccurate) ...[
              const SizedBox(height: 16),
              const Text('Select issues:'),
              Wrap(
                spacing: 8,
                children: _commonIssues.map((issue) {
                  return FilterChip(
                    label: Text(issue),
                    selected: _selectedIssues.contains(issue),
                    onSelected: (selected) {
                      setState(() {
                        if (selected) {
                          _selectedIssues.add(issue);
                        } else {
                          _selectedIssues.remove(issue);
                        }
                      });
                    },
                  );
                }).toList(),
              ),
            ],
            const SizedBox(height: 16),
            TextField(
              controller: _feedbackController,
              decoration: const InputDecoration(
                labelText: 'Additional Comments',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            final feedback = StringBuffer();
            feedback.writeln('Accuracy: ${_isAccurate ? "Yes" : "No"}');
            
            if (_selectedIssues.isNotEmpty) {
              feedback.writeln('Issues: ${_selectedIssues.join(", ")}');
            }
            
            if (_feedbackController.text.isNotEmpty) {
              feedback.writeln('Comments: ${_feedbackController.text}');
            }
            
            Navigator.of(context).pop(feedback.toString());
          },
          child: const Text('Submit'),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _feedbackController.dispose();
    super.dispose();
  }
}
