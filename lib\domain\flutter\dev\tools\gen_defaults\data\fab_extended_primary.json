{"version": "6_1_0", "md.comp.extended-fab.primary.container.color": "primaryContainer", "md.comp.extended-fab.primary.container.elevation": "md.sys.elevation.level3", "md.comp.extended-fab.primary.container.height": 56.0, "md.comp.extended-fab.primary.container.shadow-color": "shadow", "md.comp.extended-fab.primary.container.shape": "md.sys.shape.corner.large", "md.comp.extended-fab.primary.focus.container.elevation": "md.sys.elevation.level3", "md.comp.extended-fab.primary.focus.icon.color": "onPrimaryContainer", "md.comp.extended-fab.primary.focus.indicator.color": "secondary", "md.comp.extended-fab.primary.focus.indicator.outline.offset": "md.sys.state.focus-indicator.outer-offset", "md.comp.extended-fab.primary.focus.indicator.thickness": "md.sys.state.focus-indicator.thickness", "md.comp.extended-fab.primary.focus.label-text.color": "onPrimaryContainer", "md.comp.extended-fab.primary.focus.state-layer.color": "onPrimaryContainer", "md.comp.extended-fab.primary.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.extended-fab.primary.hover.container.elevation": "md.sys.elevation.level4", "md.comp.extended-fab.primary.hover.icon.color": "onPrimaryContainer", "md.comp.extended-fab.primary.hover.label-text.color": "onPrimaryContainer", "md.comp.extended-fab.primary.hover.state-layer.color": "onPrimaryContainer", "md.comp.extended-fab.primary.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.extended-fab.primary.icon.color": "onPrimaryContainer", "md.comp.extended-fab.primary.icon.size": 24.0, "md.comp.extended-fab.primary.label-text.color": "onPrimaryContainer", "md.comp.extended-fab.primary.label-text.text-style": "labelLarge", "md.comp.extended-fab.primary.lowered.container.elevation": "md.sys.elevation.level1", "md.comp.extended-fab.primary.lowered.focus.container.elevation": "md.sys.elevation.level1", "md.comp.extended-fab.primary.lowered.hover.container.elevation": "md.sys.elevation.level2", "md.comp.extended-fab.primary.lowered.pressed.container.elevation": "md.sys.elevation.level1", "md.comp.extended-fab.primary.pressed.container.elevation": "md.sys.elevation.level3", "md.comp.extended-fab.primary.pressed.icon.color": "onPrimaryContainer", "md.comp.extended-fab.primary.pressed.label-text.color": "onPrimaryContainer", "md.comp.extended-fab.primary.pressed.state-layer.color": "onPrimaryContainer", "md.comp.extended-fab.primary.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity"}