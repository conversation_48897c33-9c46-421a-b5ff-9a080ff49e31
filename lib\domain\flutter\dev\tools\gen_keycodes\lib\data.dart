// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

/// Maps iOS's special key labels to logical key names.
///
/// See https://developer.apple.com/documentation/uikit/uikeycommand/input_strings_for_special_keys?language=objc
const Map<String, String> kIosSpecialKeyMapping = <String, String>{
  'UIKeyInputEscape': 'Escape',
  'UIKeyInputF1': 'F1',
  'UIKeyInputF2': 'F2',
  'UIKeyInputF3': 'F3',
  'UIKeyInputF4': 'F4',
  'UIKeyInputF5': 'F5',
  'UIKeyInputF6': 'F6',
  'UIKeyInputF7': 'F7',
  'UIKeyInputF8': 'F8',
  'UIKeyInputF9': 'F9',
  'UIKeyInputF10': 'F10',
  'UIKeyInputF11': 'F11',
  'UIKeyInputF12': 'F12',
  'UIKeyInputUpArrow': 'ArrowUp',
  'UIKeyInputDownArrow': 'ArrowDown',
  'UIKeyInputLeftArrow': 'ArrowLeft',
  'UIKeyInputRightArrow': 'ArrowRight',
  'UIKeyInputHome': 'Home',
  'UIKeyInputEnd': 'Enter',
  'UIKeyInputPageUp': 'PageUp',
  'UIKeyInputPageDown': 'PageDown',
};
