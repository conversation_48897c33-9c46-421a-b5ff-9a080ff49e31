<!DOCTYPE HTML>
<!-- Copyright 2014 The Flutter Authors. All rights reserved.
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file. -->
<html>
<head>
  <meta charset="UTF-8">

  <title>Integration test. App load with flutter.js and onEntrypointLoaded API. Trusted Types enabled.</title>

  <!-- Enable TrustedTypes for 'script'-->
  <meta http-equiv="Content-Security-Policy" content="require-trusted-types-for 'script'">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="Web Test">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>
  <link rel="manifest" href="manifest.json">
  <script>
    // The value below is injected by flutter build, do not touch.
    var serviceWorkerVersion = null;
  </script>
  <!-- This script adds the flutter initialization JS code -->
  <script src="flutter.js" defer></script>
</head>
<body>
  <script>
    window.addEventListener('load', function(ev) {
      // Download main.dart.js
      _flutter.loader.loadEntrypoint({
        onEntrypointLoaded: onEntrypointLoaded,
        serviceWorker: {
          serviceWorkerVersion: serviceWorkerVersion,
        }
      });

      // Once the entrypoint is ready, do things!
      async function onEntrypointLoaded(engineInitializer) {
        const appRunner = await engineInitializer.initializeEngine();
        appRunner.runApp();
      }
    });
  </script>
</body>
</html>
