import 'package:flutter/material.dart';

class AppTheme {
  // Cores primárias baseadas no Material 3
  static const Color _primarySeedColor = Color(0xFF6750A4);

  // Tema claro
  static ThemeData get lightTheme => ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: _primarySeedColor,
      brightness: Brightness.light,
    ),
  );

  // Tema escuro
  static ThemeData get darkTheme => ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: _primarySeedColor,
      brightness: Brightness.dark,
    ),
  );
}
