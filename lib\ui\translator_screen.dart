import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:image_picker/image_picker.dart';
import 'package:offline_menu_translator/data/real_gemma_datasource.dart';
import 'package:offline_menu_translator/domain/download_model.dart';
import 'package:offline_menu_translator/ui/animations.dart';
import 'package:offline_menu_translator/ui/model_selection_screen.dart';

// Message class for chat
class Message {
  final String text;
  final bool isUser;
  final Uint8List? imageBytes;

  Message({required this.text, required this.isUser, this.imageBytes});

  Message.withImage({
    required this.text,
    required Uint8List imageBytes,
    required this.isUser,
  }) : imageBytes = imageBytes;
}

class TranslatorScreen extends StatefulWidget {
  final ModelInfo? modelInfo;
  final String? token;

  const TranslatorScreen({super.key, this.modelInfo, this.token});

  @override
  State<TranslatorScreen> createState() => _TranslatorScreenState();
}

class _TranslatorScreenState extends State<TranslatorScreen> {
  bool _isModelLoaded = false;
  bool _isDownloading = false;
  String? _loadingMessage = 'Checking model availability...';
  double? _downloadProgress;
  bool _isAwaitingResponse = false;
  String _selectedLanguage = 'en';
  final List<Message> _messages = [];

  Uint8List? _selectedImage;
  String? _selectedImageName;
  final ImagePicker _imagePicker = ImagePicker();
  final _textController = TextEditingController();

  late final RealGemmaDataSource _gemmaDataSource;
  bool _isModelInitialized = false;

  final Map<String, String> _supportedLanguages = {
    'en': 'English',
    'es': 'Spanish',
    'ja': 'Japanese',
    'zh': 'Chinese',
    'vi': 'Vietnamese',
    'th': 'Thai',
    'ar': 'Arabic',
    'hi': 'Hindi',
    'ko': 'Korean',
    'fi': 'Finnish',
  };

  @override
  void initState() {
    super.initState();

    // Use model info if provided, otherwise use default
    final modelInfo = widget.modelInfo;
    if (modelInfo != null) {
      _gemmaDataSource = RealGemmaDataSource(
        model: DownloadModel(
          modelUrl: modelInfo.downloadUrl,
          modelFilename: modelInfo.filename,
        ),
      );
    } else {
      // Default model - Gemma 3N que baixamos
      _gemmaDataSource = RealGemmaDataSource(
        model: DownloadModel(
          modelUrl:
              'https://huggingface.co/google/gemma-3n-E4B-it-litert-preview/resolve/main/gemma-3n-E4B-it-int4.task',
          modelFilename: 'gemma-3n-E4B-it-int4.task',
        ),
      );
    }

    _initializeModel();
  }

  Future<void> _initializeModel() async {
    try {
      setState(() {
        _loadingMessage = 'Checking if AI model is available...';
      });

      // Check if model exists
      final isModelInstalled = await _gemmaDataSource.checkModelExistence();

      if (!isModelInstalled) {
        setState(() {
          _isDownloading = true;
          _loadingMessage =
              'Downloading AI model...\nThis may take a few minutes on first use.';
          _downloadProgress = 0.0;
        });

        // Download the model using real flutter_gemma
        await _gemmaDataSource.downloadModel(
          token: widget.token ?? 'demo_token',
          onProgress: (progress) {
            if (!mounted) return;
            setState(() {
              _downloadProgress = progress;
              _loadingMessage =
                  'Downloading AI model...\n${(progress * 100).toStringAsFixed(1)}% complete';
            });
          },
        );
      }

      setState(() {
        _loadingMessage = 'Initializing AI model...';
        _downloadProgress = null;
        _isDownloading = false;
      });

      // Initialize the model using real flutter_gemma
      _isModelInitialized = await _gemmaDataSource.initializeModel();

      setState(() {
        _isModelLoaded = _isModelInitialized;
        _loadingMessage = null;
      });

      // Add welcome message
      _addWelcomeMessage();
    } catch (e) {
      setState(() {
        _loadingMessage =
            'Error loading model: $e\nPlease check your internet connection and try again.';
        _isDownloading = false;
        _downloadProgress = null;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to initialize AI model: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
            action: SnackBarAction(label: 'Retry', onPressed: _initializeModel),
          ),
        );
      }
    }
  }

  void _addWelcomeMessage() {
    final modelName = widget.modelInfo?.name ?? 'AI Assistant';
    final welcomeMessage = Message(
      text: '''Hello! I'm your AI menu translator powered by $modelName.

I can help you:
• 📸 Translate menu images to any language
• 🍽️ Explain dishes and ingredients
• 🌶️ Identify spice levels and dietary information
• 💡 Suggest similar dishes you might like

Just upload a photo of any menu and I'll translate it for you instantly!''',
      isUser: false,
    );

    setState(() {
      _messages.add(welcomeMessage);
    });
  }

  Future<void> _pickImage() async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    try {
      final pickedFile = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        final bytes = await pickedFile.readAsBytes();
        setState(() {
          _selectedImage = bytes;
          _selectedImageName = pickedFile.name;
        });
      }
    } catch (e) {
      scaffoldMessenger.showSnackBar(
        SnackBar(content: Text('Image selection error: $e')),
      );
    }
  }

  Future<void> _translateMenu() async {
    if (_selectedImage == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a menu image first')),
      );
      return;
    }

    setState(() {
      _isAwaitingResponse = true;
    });

    try {
      // Simulate translation for demo purposes
      await Future.delayed(const Duration(seconds: 2));

      final userMessage = Message.withImage(
        text:
            'Please translate this menu to ${_supportedLanguages[_selectedLanguage]}',
        imageBytes: _selectedImage!,
        isUser: true,
      );

      setState(() {
        _messages.add(userMessage);
      });

      // Simulate AI response
      final assistantResponse =
          '''
# Menu Translation

Here's the translation of your menu to ${_supportedLanguages[_selectedLanguage]}:

**Appetizers:**
- Spring Rolls - Fresh vegetables wrapped in rice paper
- Soup of the Day - Chef's special daily soup

**Main Courses:**
- Grilled Chicken - Tender chicken breast with herbs
- Vegetarian Pasta - Fresh pasta with seasonal vegetables

**Desserts:**
- Chocolate Cake - Rich chocolate layer cake
- Fresh Fruit - Seasonal fruit selection

*Note: This is a demo translation. In the full version, AI would analyze your image and provide accurate translations.*
      ''';

      setState(() {
        _messages.add(Message(text: assistantResponse, isUser: false));
      });
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Translation error: $e')));
    } finally {
      setState(() {
        _isAwaitingResponse = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: Column(
          children: [
            const Text('Menu Translator'),
            if (widget.modelInfo != null)
              Text(
                'Using ${widget.modelInfo!.name}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
          ],
        ),
        centerTitle: true,
        actions: [
          // Model info button
          if (widget.modelInfo != null)
            IconButton(
              onPressed: () => _showModelInfo(),
              icon: const Icon(Icons.info_outline),
              tooltip: 'Model Information',
            ),
          // Theme toggle button
          IconButton(
            onPressed: () {
              // Toggle theme mode - this would need to be implemented with a state management solution
              // For now, it's just a placeholder
            },
            icon: Icon(
              Theme.of(context).brightness == Brightness.dark
                  ? Icons.light_mode
                  : Icons.dark_mode,
            ),
            tooltip: 'Toggle theme',
          ),
          // Language selector with Material 3 styling
          FilledButton.tonal(
            onPressed: () => _showLanguageSelector(context),
            style: FilledButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.language, size: 18),
                const SizedBox(width: 4),
                Text(_supportedLanguages[_selectedLanguage]!),
              ],
            ),
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: !_isModelLoaded
          ? _buildLoadingScreen()
          : Column(
              children: [
                Expanded(
                  child: _messages.isEmpty
                      ? _buildEmptyState()
                      : ListView.builder(
                          reverse: true,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16.0,
                            vertical: 16.0,
                          ),
                          itemCount: _messages.length,
                          itemBuilder: (context, index) {
                            final message =
                                _messages[_messages.length - 1 - index];
                            final isLastMessage = index == 0;
                            final isFirstMessage =
                                index == _messages.length - 1;

                            return FadeInAnimation(
                              duration: AppAnimations.medium2,
                              curve: AppAnimations.emphasized,
                              child: ChatMessageWidget(
                                message: message,
                                isLastMessage: isLastMessage,
                                isFirstMessage: isFirstMessage,
                              ),
                            );
                          },
                        ),
                ),
                if (_isAwaitingResponse)
                  FadeInAnimation(
                    duration: AppAnimations.short4,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        children: [
                          SizedBox.square(
                            dimension: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'AI is thinking...',
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(
                                  color: Theme.of(
                                    context,
                                  ).colorScheme.onSurfaceVariant,
                                ),
                          ),
                        ],
                      ),
                    ),
                  ),
                _buildChatInputArea(),
              ],
            ),
    );
  }

  Widget _buildLoadingScreen() {
    final colorScheme = Theme.of(context).colorScheme;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // AI Brain Icon
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: colorScheme.primaryContainer,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.psychology,
                size: 64,
                color: colorScheme.onPrimaryContainer,
              ),
            ),
            const SizedBox(height: 32),

            // Loading indicator
            if (_isDownloading)
              SizedBox(
                width: 200,
                child: LinearProgressIndicator(
                  value: _downloadProgress,
                  backgroundColor: colorScheme.surfaceContainerHighest,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    colorScheme.primary,
                  ),
                ),
              )
            else
              CircularProgressIndicator(color: colorScheme.primary),

            const SizedBox(height: 24),

            // Status message
            Text(
              _loadingMessage ?? 'Loading...',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 16),

            // Progress percentage for downloads
            if (_downloadProgress != null)
              Text(
                '${(_downloadProgress! * 100).toStringAsFixed(1)}% complete',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),

            const SizedBox(height: 24),

            // Information card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: colorScheme.primary,
                      size: 24,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _isDownloading
                          ? 'First-time setup: Downloading AI model'
                          : 'Preparing AI translation engine',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: colorScheme.onSurface,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _isDownloading
                          ? 'The AI model is being downloaded. This only happens once and enables offline translation.'
                          : 'Setting up the translation engine for optimal performance.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChatInputArea() {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        border: Border(
          top: BorderSide(color: colorScheme.outlineVariant, width: 1),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (_selectedImage != null)
                Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Stack(
                    alignment: Alignment.topRight,
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: Image.memory(
                          _selectedImage!,
                          height: 120,
                          width: 120,
                          fit: BoxFit.cover,
                        ),
                      ),
                      Material(
                        color: Colors.black54,
                        borderRadius: BorderRadius.circular(20),
                        child: InkWell(
                          borderRadius: BorderRadius.circular(20),
                          onTap: () {
                            setState(() => _selectedImage = null);
                          },
                          child: const Padding(
                            padding: EdgeInsets.all(4.0),
                            child: Icon(
                              Icons.close,
                              color: Colors.white,
                              size: 18,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              Row(
                children: [
                  IconButton.filledTonal(
                    icon: const Icon(Icons.image_outlined),
                    onPressed: _pickImage,
                    tooltip: 'Select image',
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      controller: _textController,
                      decoration: const InputDecoration(
                        hintText: 'Add context or ask questions...',
                      ),
                      maxLines: null,
                      textInputAction: TextInputAction.send,
                      onSubmitted: (_) =>
                          _isAwaitingResponse ? null : _sendMessage(),
                    ),
                  ),
                  const SizedBox(width: 8),
                  FilledButton.icon(
                    onPressed: _isAwaitingResponse ? null : _sendMessage,
                    icon: _isAwaitingResponse
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.send, size: 18),
                    label: Text(_isAwaitingResponse ? 'Sending...' : 'Send'),
                    style: FilledButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _sendMessage() async {
    final text = _textController.text.trim();
    final image = _selectedImage;

    if (text.isEmpty && image == null) {
      return;
    }

    if (_isAwaitingResponse) return;

    // Check if model is ready
    if (!_isModelInitialized) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please wait for model to load')),
      );
      return;
    }

    setState(() {
      _isAwaitingResponse = true;
    });

    // Create the user's message
    final prompt = text.isNotEmpty
        ? text
        : "Please translate this menu to ${_supportedLanguages[_selectedLanguage]}";

    final userMessage = image != null
        ? Message.withImage(text: prompt, imageBytes: image, isUser: true)
        : Message(text: prompt, isUser: true);

    // Add user message and clear inputs
    setState(() {
      _messages.add(userMessage);
      _selectedImage = null;
      _selectedImageName = null;
    });

    _textController.clear();
    FocusScope.of(context).unfocus();

    try {
      // Add placeholder for AI response
      final responsePlaceholder = Message(text: '', isUser: false);
      setState(() {
        _messages.add(responsePlaceholder);
      });

      // Get response stream from real flutter_gemma
      final responseStream = _gemmaDataSource.generateResponse(
        prompt,
        imageBytes: image,
      );

      await for (final token in responseStream) {
        if (!mounted) return;
        setState(() {
          // Update the last message with new token
          final lastMessage = _messages.last;
          final updatedText = lastMessage.text + token;
          _messages[_messages.length - 1] = Message(
            text: updatedText,
            isUser: false,
          );
        });
      }
    } catch (e) {
      debugPrint("Error during chat generation: $e");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error generating response: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
        // Remove empty AI message placeholder on error
        setState(() {
          if (_messages.isNotEmpty && !_messages.last.isUser) {
            _messages.removeLast();
          }
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isAwaitingResponse = false;
        });
      }
    }
  }

  Widget _buildEmptyState() {
    final colorScheme = Theme.of(context).colorScheme;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: StaggeredListAnimation(
          duration: AppAnimations.medium3,
          staggerDelay: AppAnimations.short3,
          children: [
            ScaleInAnimation(
              duration: AppAnimations.medium4,
              curve: AppAnimations.emphasized,
              child: Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: colorScheme.primaryContainer.withOpacity(0.3),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.translate,
                  size: 48,
                  color: colorScheme.primary,
                ),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Welcome to Menu Translator',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              'Take a photo of any menu and I\'ll translate it for you instantly!',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ScaleInAnimation(
              begin: 0.9,
              duration: AppAnimations.medium2,
              curve: AppAnimations.emphasizedDecelerate,
              child: FilledButton.icon(
                onPressed: _pickImage,
                icon: const Icon(Icons.camera_alt),
                label: const Text('Take Photo'),
                style: FilledButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showLanguageSelector(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => SlideInAnimation(
        duration: AppAnimations.medium2,
        curve: AppAnimations.emphasizedDecelerate,
        begin: const Offset(0, 0.3),
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              FadeInAnimation(
                duration: AppAnimations.short4,
                child: Text(
                  'Select Language',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
              ),
              const SizedBox(height: 16),
              StaggeredListAnimation(
                duration: AppAnimations.short4,
                staggerDelay: AppAnimations.short1,
                children: _supportedLanguages.entries
                    .map(
                      (entry) => ListTile(
                        leading: Icon(
                          _selectedLanguage == entry.key
                              ? Icons.radio_button_checked
                              : Icons.radio_button_unchecked,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        title: Text(entry.value),
                        onTap: () {
                          setState(() {
                            _selectedLanguage = entry.key;
                          });
                          Navigator.pop(context);
                        },
                      ),
                    )
                    .toList(),
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  void _showModelInfo() {
    if (widget.modelInfo == null) return;

    final model = widget.modelInfo!;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        icon: const Icon(Icons.psychology),
        title: Text(model.name),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                model.description,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Icon(
                    Icons.storage,
                    size: 16,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Size: ${model.size}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                'Capabilities:',
                style: Theme.of(context).textTheme.titleSmall,
              ),
              const SizedBox(height: 8),
              ...model.capabilities.map(
                (capability) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        size: 16,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          capability,
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          FilledButton.icon(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => const ModelSelectionScreen(),
                ),
              );
            },
            icon: const Icon(Icons.swap_horiz),
            label: const Text('Change Model'),
          ),
        ],
      ),
    );
  }
}

class ChatMessageWidget extends StatelessWidget {
  final Message message;
  final bool isLastMessage;
  final bool isFirstMessage;

  const ChatMessageWidget({
    super.key,
    required this.message,
    this.isLastMessage = false,
    this.isFirstMessage = false,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isUser = message.isUser;
    final alignment = isUser ? Alignment.centerRight : Alignment.centerLeft;

    // Material 3 chat bubble styling
    final backgroundColor = isUser
        ? colorScheme.primaryContainer
        : colorScheme.surfaceContainerHighest;
    final textColor = isUser
        ? colorScheme.onPrimaryContainer
        : colorScheme.onSurface;

    final borderRadius = BorderRadius.only(
      topLeft: const Radius.circular(20),
      topRight: const Radius.circular(20),
      bottomLeft: isUser ? const Radius.circular(20) : const Radius.circular(4),
      bottomRight: isUser
          ? const Radius.circular(4)
          : const Radius.circular(20),
    );

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeOutCubic,
      child: Align(
        alignment: alignment,
        child: Container(
          constraints: BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width * 0.85,
          ),
          margin: EdgeInsets.only(
            top: isFirstMessage ? 8 : 4,
            bottom: isLastMessage ? 8 : 4,
            left: isUser ? 48 : 0,
            right: isUser ? 0 : 48,
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: borderRadius,
            boxShadow: [
              BoxShadow(
                color: colorScheme.shadow.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (message.imageBytes != null)
                Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.memory(
                      message.imageBytes!,
                      width: 200,
                      height: 200,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              if (message.text.isNotEmpty)
                MarkdownBody(
                  data: message.text,
                  styleSheet: MarkdownStyleSheet.fromTheme(
                    Theme.of(context),
                  ).copyWith(p: TextStyle(color: textColor, fontSize: 15)),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
