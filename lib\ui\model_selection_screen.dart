import 'package:flutter/material.dart';
import 'package:offline_menu_translator/domain/download_model.dart';
import 'package:offline_menu_translator/ui/animations.dart';
import 'package:offline_menu_translator/ui/model_download_screen.dart';
import 'package:offline_menu_translator/services/model_storage_service.dart';
import 'package:offline_menu_translator/ui/translator_screen.dart';

class ModelSelectionScreen extends StatefulWidget {
  const ModelSelectionScreen({super.key});

  @override
  State<ModelSelectionScreen> createState() => _ModelSelectionScreenState();
}

class _ModelSelectionScreenState extends State<ModelSelectionScreen> {
  final TextEditingController _tokenController = TextEditingController();
  String? _selectedModelId;
  bool _isTokenValid = false;
  Set<String> _downloadedModels = {};

  // Available models with their specifications
  final List<ModelInfo> _availableModels = [
    ModelInfo(
      id: 'gemma-3n-E2B-it',
      name: 'Gemma 3N E2B Instruct',
      description: 'Compact model optimized for mobile and web',
      size: '800 MB',
      capabilities: [
        'Fast Translation',
        'Multi-language Support',
        'Mobile Optimized',
      ],
      downloadUrl:
          'https://huggingface.co/google/gemma-3n-E2B-it-litert-preview/resolve/main/gemma-3n-E2B-it-int4.task',
      filename: 'gemma-3n-E2B-it-int4.task',
      recommended: false,
    ),
    ModelInfo(
      id: 'gemma-3n-E4B-it',
      name: 'Gemma 3N E4B Instruct',
      description:
          'Premium model with enhanced translation capabilities and vision support',
      size: '1.2 GB',
      capabilities: [
        'Premium Translation',
        'Image Understanding',
        'Cultural Context',
        'Advanced Reasoning',
      ],
      downloadUrl:
          'https://huggingface.co/google/gemma-3n-E4B-it-litert-preview/resolve/main/gemma-3n-E4B-it-int4.task',
      filename: 'gemma-3n-E4B-it-int4.task',
      recommended: true,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _loadDownloadedModels();
  }

  Future<void> _loadDownloadedModels() async {
    final downloaded = await ModelStorageService.instance.getDownloadedModels();
    setState(() {
      _downloadedModels = downloaded.toSet();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(title: const Text('Select AI Model'), centerTitle: true),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Token input section
            _buildTokenSection(),
            const SizedBox(height: 24),

            // Models list
            Text(
              'Available Models',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),

            Expanded(
              child: ListView.builder(
                itemCount: _availableModels.length,
                itemBuilder: (context, index) {
                  final model = _availableModels[index];
                  return FadeInAnimation(
                    duration: AppAnimations.medium2,
                    child: _buildModelCard(model),
                  );
                },
              ),
            ),

            // Continue button
            SafeArea(
              child: Column(
                children: [
                  // Status indicator
                  if (!_canProceed())
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            size: 16,
                            color: Theme.of(
                              context,
                            ).colorScheme.onSurfaceVariant,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _selectedModelId == null
                                  ? 'Please select a model'
                                  : !_isTokenValid
                                  ? 'Please enter a valid token'
                                  : 'Ready to proceed',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.onSurfaceVariant,
                                  ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  SizedBox(
                    width: double.infinity,
                    child: FilledButton.icon(
                      onPressed: _canProceed() ? _proceedWithSelection : null,
                      icon: const Icon(Icons.download),
                      label: const Text('Download & Continue'),
                      style: FilledButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTokenSection() {
    final colorScheme = Theme.of(context).colorScheme;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.key, color: colorScheme.primary, size: 24),
                const SizedBox(width: 8),
                Text(
                  'HuggingFace Access Token',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            TextField(
              controller: _tokenController,
              decoration: InputDecoration(
                hintText: 'Enter HuggingFace token (hf_...)...',
                prefixIcon: const Icon(Icons.vpn_key),
                suffixIcon: _isTokenValid
                    ? Icon(Icons.check_circle, color: colorScheme.primary)
                    : IconButton(
                        icon: const Icon(Icons.help_outline),
                        onPressed: _showTokenHelp,
                      ),
                helperText:
                    'Use "test" or "demo" for testing, or get real token at huggingface.co/settings/tokens',
              ),
              obscureText: false, // Changed to false for easier testing
              onChanged: _validateToken,
            ),
            const SizedBox(height: 8),
            Text(
              'Required to download models from HuggingFace Hub',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 8),
            TextButton.icon(
              onPressed: () => _showTokenHelp(),
              icon: const Icon(Icons.help_outline, size: 16),
              label: const Text('How to get a token?'),
              style: TextButton.styleFrom(
                padding: EdgeInsets.zero,
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModelCard(ModelInfo model) {
    final colorScheme = Theme.of(context).colorScheme;
    final isSelected = _selectedModelId == model.id;
    final isDownloaded = _downloadedModels.contains(model.id);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _selectModel(model.id),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              model.name,
                              style: Theme.of(context).textTheme.titleMedium
                                  ?.copyWith(
                                    color: colorScheme.onSurface,
                                    fontWeight: FontWeight.w600,
                                  ),
                            ),
                            const SizedBox(width: 8),
                            if (isDownloaded) ...[
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: colorScheme.secondaryContainer,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  'DOWNLOADED',
                                  style: Theme.of(context).textTheme.labelSmall
                                      ?.copyWith(
                                        color: colorScheme.onSecondaryContainer,
                                        fontWeight: FontWeight.w600,
                                      ),
                                ),
                              ),
                              const SizedBox(width: 8),
                            ],
                            if (model.recommended) ...[
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: colorScheme.primaryContainer,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  'RECOMMENDED',
                                  style: Theme.of(context).textTheme.labelSmall
                                      ?.copyWith(
                                        color: colorScheme.onPrimaryContainer,
                                        fontWeight: FontWeight.w600,
                                      ),
                                ),
                              ),
                            ],
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          model.description,
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(color: colorScheme.onSurfaceVariant),
                        ),
                      ],
                    ),
                  ),
                  if (isDownloaded)
                    FilledButton.icon(
                      onPressed: () => _goToChat(model),
                      icon: const Icon(Icons.chat, size: 16),
                      label: const Text('Use'),
                      style: FilledButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                      ),
                    )
                  else
                    Radio<String>(
                      value: model.id,
                      groupValue: _selectedModelId,
                      onChanged: (value) => _selectModel(value!),
                    ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(
                    Icons.storage,
                    size: 16,
                    color: colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    model.size,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Icon(
                    Icons.star,
                    size: 16,
                    color: colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${model.capabilities.length} features',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: model.capabilities.map((capability) {
                  return Chip(
                    label: Text(
                      capability,
                      style: Theme.of(context).textTheme.labelSmall,
                    ),
                    backgroundColor: isSelected
                        ? colorScheme.secondaryContainer
                        : colorScheme.surfaceContainerHighest,
                    side: BorderSide.none,
                  );
                }).toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _validateToken(String token) {
    setState(() {
      // Allow both real HF tokens and test tokens for development
      _isTokenValid =
          token.length > 5 &&
          (token.startsWith('hf_') ||
              token.toLowerCase() == 'test' ||
              token.toLowerCase() == 'demo');
    });
  }

  void _showTokenHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        icon: const Icon(Icons.help_outline),
        title: const Text('How to get HuggingFace Token'),
        content: const SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'To download models, you need a HuggingFace access token:',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              SizedBox(height: 16),
              Text('1. Go to huggingface.co'),
              SizedBox(height: 8),
              Text('2. Create an account or sign in'),
              SizedBox(height: 8),
              Text('3. Go to Settings → Access Tokens'),
              SizedBox(height: 8),
              Text('4. Create a new token with "Read" permissions'),
              SizedBox(height: 8),
              Text('5. Copy the token (starts with "hf_")'),
              SizedBox(height: 16),
              Text(
                'Note: The token is required to download models from HuggingFace Hub.',
                style: TextStyle(fontStyle: FontStyle.italic),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  void _selectModel(String modelId) {
    setState(() {
      _selectedModelId = modelId;
    });
  }

  void _goToChat(ModelInfo model) async {
    // Set as active model
    await ModelStorageService.instance.setActiveModel(model.id, {
      'name': model.name,
      'description': model.description,
      'size': model.size,
      'capabilities': model.capabilities,
    });

    // Navigate to chat
    if (mounted) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => TranslatorScreen(modelInfo: model),
        ),
      );
    }
  }

  bool _canProceed() {
    return _isTokenValid && _selectedModelId != null;
  }

  void _proceedWithSelection() {
    try {
      final selectedModel = _availableModels.firstWhere(
        (model) => model.id == _selectedModelId,
      );

      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => ModelDownloadScreen(
            model: selectedModel,
            token: _tokenController.text,
          ),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: $e'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }
}

class ModelInfo {
  final String id;
  final String name;
  final String description;
  final String size;
  final List<String> capabilities;
  final String downloadUrl;
  final String filename;
  final bool recommended;

  ModelInfo({
    required this.id,
    required this.name,
    required this.description,
    required this.size,
    required this.capabilities,
    required this.downloadUrl,
    required this.filename,
    required this.recommended,
  });
}

// Placeholder for ModelDownloadScreen - will be implemented next
class ModelDownloadScreen extends StatelessWidget {
  final ModelInfo model;
  final String token;

  const ModelDownloadScreen({
    super.key,
    required this.model,
    required this.token,
  });

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(child: Text('Download Screen - To be implemented')),
    );
  }
}
