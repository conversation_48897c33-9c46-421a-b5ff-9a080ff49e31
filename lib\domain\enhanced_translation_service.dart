import 'package:flutter_gemma/flutter_gemma.dart';
import 'package:flutter_gemma/core/chat.dart';
import 'package:flutter_gemma/core/model.dart';
import 'dart:typed_data';
import 'language_support.dart';

class EnhancedTranslationService {
  final InferenceModel model;
  final InferenceChat chat;

  EnhancedTranslationService({
    required this.model,
    required this.chat,
  });

  static Future<EnhancedTranslationService> initialize() async {
    final gemma = FlutterGemmaPlugin.instance;
    
    final model = await gemma.createModel(
      modelType: ModelType.gemmaIt,
      supportImage: true,
      maxTokens: 4096, // Increased for better handling of long translations
      temperature: 0.7, // Better balance between creativity and accuracy
      topK: 40, // Improved sampling for rare words/characters
      contextWindow: 8192, // Larger context window for better understanding
    );

    final chat = await model.createChat(supportImage: true);
    
    return EnhancedTranslationService(
      model: model,
      chat: chat,
    );
  }

  Future<Stream<String>> translateMenu({
    required Uint8List? imageBytes,
    required String targetLanguage,
    String? additionalContext,
  }) async {
    final prompt = _buildTranslationPrompt(targetLanguage, additionalContext);
    
    final message = Message.withImage(
      text: prompt,
      imageBytes: imageBytes,
      isUser: true,
    );

    await chat.addQueryChunk(message);
    return chat.generateChatResponseAsync();
  }

  String _buildTranslationPrompt(String targetLanguage, String? additionalContext) {
    final basePrompt = '''
You are a specialized menu translator with expertise in culinary terminology and cultural context.
Please analyze this menu image and:
1. Identify the source language
2. Translate each dish name and description to $targetLanguage
3. Preserve any special characters or diacritical marks
4. Include pronunciation guides for complex terms
5. Add brief cultural context for unique dishes
6. Note any dietary indicators (vegetarian, spicy, etc.)

Format the translation as follows:
- Original: [original text]
- Translation: [translated text]
- Pronunciation: [if applicable]
- Cultural Notes: [if relevant]
- Dietary Notes: [if any]
''';

    if (LanguageSupport.tonalLanguages.contains(targetLanguage)) {
      return '$basePrompt\nPlease pay special attention to tonal markers and include tone numbers/marks in the pronunciation guide.';
    }

    if (LanguageSupport.complexScripts.contains(targetLanguage)) {
      return '$basePrompt\nPlease ensure proper rendering of complex scripts and include transliteration if helpful.';
    }

    if (additionalContext != null) {
      return '$basePrompt\nAdditional Context: $additionalContext';
    }

    return basePrompt;
  }

  Future<void> improveWithCommunityFeedback({
    required String originalText,
    required String translation,
    required String feedback,
  }) async {
    // Store feedback for future model improvements
    // This would integrate with a feedback collection system
    final message = Message(
      text: '''
Learning from community feedback:
Original: $originalText
Translation: $translation
Feedback: $feedback
Please analyze this feedback and suggest improvements for future translations.
''',
      isUser: true,
    );

    await chat.addQueryChunk(message);
  }
}
